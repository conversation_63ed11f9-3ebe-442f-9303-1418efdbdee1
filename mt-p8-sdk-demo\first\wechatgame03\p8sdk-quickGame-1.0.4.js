// ========================================================== 查询渠道start =========================================

let P8QuickGameSDK_VERSION = "1.0.4"; // 2025-7-30 13:53:06
console.log('[聚合SDK] 版本号 >', P8QuickGameSDK_VERSION);
console.log('[聚合SDK] 更新说明 >', "新增美团小游戏渠道");
let Global;
if (typeof window != "undefined") {
  Global = window
} else if (typeof GameGlobal != "undefined") {
  Global = GameGlobal
} else if (typeof global != "undefined") {
  Global = global
} else {
  Global = {};
  console.log("[聚合SDK] 未知渠道,请联系SDK开发者")
}

// ========================================================== 查询渠道end ===========================================

// ========================================================== 查询平台start =========================================

if (typeof qg != "undefined") {
  Global.channel = "QuickGame";
  Global.pf = "quickGame";
  Global.PFSDK = qg
} else if (typeof wx != "undefined") {
  Global.channel = "Weixin";
  Global.pf = "weixin";
  Global.PFSDK = wx;
} else if (typeof mt != "undefined") {
  Global.channel = "Meituan";
  Global.pf = "meituan";
  Global.PFSDK = mt;
} else {
  Global.channel = "";
  Global.PFSDK = window;
  console.log("[聚合SDK] 未知平台,请联系SDK开发者")
}



// ========================================================== 查询平台end ===========================================

// ========================================================== 数据start ===============================================
let P8_QuickGame_Data = {
  systeminfo: {}, // 系统信息
  launchOptions: {}, // 启动参数
  engineVersion: '', // 引擎版本
  model: '', // 设备型号
  device_model: '', // 设备型号
  device_version: '', // 设备版本
  device_resolution: '', // 设备分辨率
  device_net: '', // 设备网络
  platform: '', // 平台
  channelid: '', // 渠道id
  site: '', // site
  key: '', // key
  aid: '', // aid
  appid: '', // appid
  pkgName: '', // 包名
  game_id: '', // 游戏id
  appName: '', // 游戏名
  data_url: '', // 日志url
  rg_url: '', // 支付url
  platform_url: '', // 平台url
  dsj_url: '', // 大数据url
  extraInfo: '', // 额外信息
  appKey: '', // 应用key
  appSecret: '', // 应用secret
  backcallUrl: '', // 回调url
  playerId: '', // 玩家id
  device: '', // 设备id
  uid: '', // 用户id
  account: '', // 用户名
  token: '', // 用户token
  mac: '', // 设备mac
  ip: '', // 设备ip
  modeltype: 'android', // 设备类型
  gameversion: '', // 游戏版本
  sid: '', // 区服id
  roleid: '', // 角色id
  rolename: '', // 角色名
  level: '', // 等级
  vip: '', // vip
  CPid: '', // 商户id
  publicKey: '', // 公钥
  appKey: '', // 应用key
  appSecret: '', // 应用secret
  backcallUrl: '', // 回调url
  nonce: '', // 随机数
  // 广告
  ad_positon: "",
  ad_unit_id: "",
  ad_slot: "",
  adList: {
    ad_unit_id_reward: "",
    ad_slot_reward: "",
  },
};

// ========================================================== 数据end =================================================

// ========================================================== 初始化start ===========================================
let PFSDK = Global.PFSDK;
let P8QuickGameSDK = {};
let P8QuickGameLogSDK = {};
let P8OppoSDK = {};
let p8Vivosdk = {};
let P8HonorSDK = {};
let P8HuaweiRunTimeSDK = {};
let P8MeituanSDK = {};

const platformSDKMap = {
  "oppo": P8OppoSDK,
  "vivo": p8Vivosdk,
  "honor": P8HonorSDK,
  "huaweiRuntime": P8HuaweiRunTimeSDK,
  "meituan": P8MeituanSDK
};

let loginResult = null;
let start_param = "";
let ad_show_time = 0;
let queryData = {
  weixinadinfo: "",
  gdt_vid: "",
  aid: "",
  code: "",
  c: "",
};
let loginRetryTimer = null;

let qg_videoAD;
// 添加广告状态管理
let adLoadState = {
  isLoading: false,
  isReady: false
};
// 添加激励视频状态锁
let isVideoLogReported = false;
let isVideoShowInProgress = false;
let lastVideoShowTime = 0;
const VIDEO_SHOW_COOLDOWN = 1000; // 1秒冷却时间
let lastVideoReportTime = 0; // 添加静态变量存储上一次上报时间

// 华为RunTime Data
let huaweiRunTimeData = {
  isInGame: false,
  randomNumStr: Math.round(9999999999 * Math.random()),
  transactionId: 0, //防沉迷，仅当eventType为GAMEBEGIN时，返回对应的transactionId
  isAdult: true, //用户是否成年
  playerDuration: 0, //玩家当天游戏时长。(分钟)
  maxChildrenPlayerTime: 120, //未成年游戏每日2小时
  inAppPurchaseData: [], //消耗型商品
}

// console.log('[聚合SDK] PFSDK >', PFSDK)

function init() {
  if (!!PFSDK.getSystemInfoSync) {
    P8_QuickGame_Data.systeminfo = PFSDK.getSystemInfoSync();
    P8_QuickGame_Data.engineVersion = P8_QuickGame_Data.systeminfo.platformVersionCode;
    P8_QuickGame_Data.model = P8_QuickGame_Data.systeminfo.model;
    P8_QuickGame_Data.device_model = P8_QuickGame_Data.systeminfo.model;
    P8_QuickGame_Data.device_version = P8_QuickGame_Data.systeminfo.system;
    P8_QuickGame_Data.device_resolution = P8_QuickGame_Data.systeminfo.screenWidth + "*" + P8_QuickGame_Data.systeminfo.screenHeight;
    P8_QuickGame_Data.device_net = P8_QuickGame_Data.systeminfo.wifiSignal;
  } else {
    P8_QuickGame_Data.systeminfo = {}
  }

  if (!!PFSDK.getLaunchOptionsSync) {
    P8_QuickGame_Data.launchOptions = PFSDK.getLaunchOptionsSync()
  } else {
    P8_QuickGame_Data.launchOptions = {}
  }

  if (!!PFSDK.getNetworkType) {
    if (!P8_QuickGame_Data.device_net) {
      console.log('[聚合SDK] 获取不到网络类型,从getNetworkType获取')
      PFSDK.getNetworkType({
        success: function (res) {
          console.log('[聚合SDK] 获取网络类型成功 >', res.networkType)
          P8_QuickGame_Data.device_net = res.networkType;
        },
        fail: function (res) {
          console.log('[聚合SDK] 获取网络类型失败 >', res.errMsg);
        },
        complete: function (res) {},
      });
    }
  } else {
    P8_QuickGame_Data.device_net = "";
  }


  // console.log('[聚合SDK] P8_QuickGame_Data >', P8_QuickGame_Data)
  console.log('[聚合SDK] P8_QuickGame_Data >', advancedObjectToString(P8_QuickGame_Data))
}

init();

// ========================================================== 初始化end ===============================================

// ========================================================== 快游戏SDK初始化 ===============================================

P8QuickGameSDK.init = function (a) {
  var t = ["aid", "appid", "key", "site", "pkgName", "platform", "channelid"];
  for (const r of t) {
    if (!a[r] || a[r] == "") {
      var n = {
        result: 1,
        data: {
          msg: `${r}不能为空`
        }
      };
      return new Promise((e, a) => {
        e(n)
      })
    }
  }
  P8_QuickGame_Data.site = a.site;
  P8_QuickGame_Data.key = a.key;
  P8_QuickGame_Data.aid = a.aid;
  P8_QuickGame_Data.channelid = a.channelid;
  P8_QuickGame_Data.appid = a.appid;
  P8_QuickGame_Data.appName = a.appName;
  P8_QuickGame_Data.pkgName = a.pkgName;
  P8_QuickGame_Data.game_id = a.aid.slice(-4);
  P8_QuickGame_Data.platform = a.platform;
  P8QuickGameSDK.print("site:" + a.site);
  P8QuickGameSDK.print("key:" + a.key);
  P8QuickGameSDK.print("aid:" + a.aid);
  P8QuickGameSDK.print("appid:" + a.appid);
  P8QuickGameSDK.print("appName:" + a.appName);
  P8QuickGameSDK.print("pkgName:" + a.pkgName);
  P8QuickGameSDK.print("game_id:" + P8_QuickGame_Data.game_id);
  P8QuickGameSDK.print("platform:" + P8_QuickGame_Data.platform);
  P8QuickGameSDK.print("channelid:" + P8_QuickGame_Data.channelid);

  // 获取当前平台对应的SDK
  const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

  return new Promise((resolve, reject) => {
    if (P8_QuickGame_Data.platform === 'huaweiRuntime') {
      P8HuaweiRunTimeSDK.RunTimeInit().then((res) => {
        resolve(res);
      }).catch((err) => {
        reject(err);
      })
    } else {
      if (currentSDK && currentSDK.init) {
        currentSDK.init().then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        })
      } else {
        resolve({
          result: 0,
          data: {
            msg: '初始化成功'
          }
        })
      }
    }
  })
}

// ========================================================== 快游戏SDK初始化end ===============================================

// ========================================================== 登录start ===============================================

P8QuickGameSDK.login = function (_retryCount = 0, _maxRetries = 3, _retryDelay = 1000) {
  // 清除之前的重试定时器（如果存在）
  if (loginRetryTimer) {
    console.log('[聚合SDK] 清除之前的登录重试定时器');
    clearTimeout(loginRetryTimer);
    loginRetryTimer = null;
  }

  let result = new Promise((resolve, reject) => {
    console.log('[ P8_QuickGame_Data.platform ] >', P8_QuickGame_Data.platform)
    if (!P8_QuickGame_Data.platform) {
      resolve({
        result: 1,
        data: {
          msg: '请重新初始化SDK,platform平台不能为空'
        }
      })
      return;
    }

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其Login方法
    if (currentSDK) {
      currentSDK.Login().then((res) => {
        // 检查登录是否成功
        if (res.result !== 0 && _retryCount < _maxRetries) {
          console.log(`[聚合SDK] 登录失败，${_retryDelay / 1000}秒后进行第${_retryCount + 1}次重试...`);

          // 延迟后重试
          loginRetryTimer = setTimeout(() => {
            // 重置定时器引用
            loginRetryTimer = null;

            P8QuickGameSDK.login(_retryCount + 1, _maxRetries, _retryDelay)
              .then(retryRes => resolve(retryRes))
              .catch(err => reject(err));
          }, _retryDelay);
        } else {
          // 登录成功或已达到最大重试次数
          if (res.result !== 0) {
            console.log(`[聚合SDK] 登录失败，已重试${_retryCount}次，不再重试`);
          } else if (_retryCount > 0) {
            console.log(`[聚合SDK] 第${_retryCount}次重试登录成功`);
          }
          resolve(res);
        }
      }).catch(err => {
        // 处理异常情况
        console.error(`[登录错误] ${P8_QuickGame_Data.platform}登录失败:`, err);

        // 异常情况也进行重试
        if (_retryCount < _maxRetries) {
          console.log(`[聚合SDK] 登录异常，${_retryDelay / 1000}秒后进行第${_retryCount + 1}次重试...`);

          loginRetryTimer = setTimeout(() => {
            // 重置定时器引用
            loginRetryTimer = null;

            P8QuickGameSDK.login(_retryCount + 1, _maxRetries, _retryDelay)
              .then(retryRes => resolve(retryRes))
              .catch(retryErr => reject(retryErr));
          }, _retryDelay);
        } else {
          resolve({
            result: 1,
            data: {
              msg: `${P8_QuickGame_Data.platform}登录失败，已重试${_retryCount}次`,
              error: err
            }
          });
        }
      });
    } else {
      // 处理未知平台的情况
      console.error(`[登录错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  // 添加Promise完成后的清理逻辑
  result.finally(() => {
    // 如果这是最外层调用（非递归调用），确保在Promise完成后清理定时器
    if (_retryCount === 0 && loginRetryTimer) {
      clearTimeout(loginRetryTimer);
      loginRetryTimer = null;
    }
  });

  return result;
}

// ========================================================== 登录end =================================================

// ========================================================== 支付start =================================================

P8QuickGameSDK.pay = function (a) {
  console.log('[ 进入支付 ] >', advancedObjectToString(a))
  let result = new Promise((resolve, reject) => {
    console.log('[ P8_QuickGame_Data.platform ] >', P8_QuickGame_Data.platform)

    if (!P8_QuickGame_Data.platform) {
      resolve({
        result: 1,
        data: {
          msg: '请重新初始化SDK,platform平台不能为空'
        }
      })
      return;
    }

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其pay方法
    if (currentSDK) {
      currentSDK.pay(a).then((res) => {
        resolve(res);
      }).catch(err => {
        // 添加错误处理
        console.error(`[支付错误] ${P8_QuickGame_Data.platform}支付失败:`, err);
        resolve({
          result: 1,
          data: {
            msg: `${P8_QuickGame_Data.platform}支付失败`,
            error: err
          }
        });
      });
    } else {
      // 处理未知平台的情况
      console.error(`[支付错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  return result;
}

// ========================================================== 支付end =================================================

// ========================================================== 广告start =================================================

P8QuickGameSDK.videoADInit = function (a) {
  let result = new Promise((resolve, reject) => {
    if (!P8_QuickGame_Data.platform) {
      console.error('[聚合SDK] 广告初始化错误 平台不能为空')
      return;
    }

    // 获取当前平台对应的SDK
    const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

    // 如果找到对应平台的SDK，则调用其adInitalization方法
    if (currentSDK) {
      currentSDK.adInitalization(a);
    } else {
      // 处理未知平台的情况
      console.error(`[广告初始化错误] 未知平台: ${P8_QuickGame_Data.platform}`);
      resolve({
        result: 1,
        data: {
          msg: `未知平台: ${P8_QuickGame_Data.platform}`
        }
      });
    }
  })

  return result;
}

P8QuickGameSDK.videoADShow = function (a, b, c, d, e) {
  if (!P8_QuickGame_Data.platform) {
    console.error('[聚合SDK] 广告播放错误 平台不能为空')
    return;
  }

  // 获取当前平台对应的SDK
  const currentSDK = platformSDKMap[P8_QuickGame_Data.platform];

  // 如果找到对应平台的SDK，则调用其videoADShow方法
  if (currentSDK) {
    currentSDK.videoADShow(
      () => a(),
      () => b(),
      (err) => c(err),
      () => d(),
      e
    );
  }
}


// ========================================================== 广告end =================================================


// ========================================================== oppo SDK start =================================================
P8OppoSDK.Login = function () {
  let result = new Promise((resolve, reject) => {
    let a = "https://center.play800.cn/fast_game_callback/getaliyuntxt";
    P8OppoSDK.XmlHttpRequest(a + "?url=https://ks3-cn-shanghai.ksyun.com/aliyun/93615202390552966.txt", "GET", null, a => {
      console.log("请求术良数据" + JSON.stringify(a));
      if (a.url_address) {
        console.log('[ 获取服务器数据 ] >', a)
        var t = a.url_address.data_url;
        var n = a.url_address.rg_url;
        var i = a.url_address.platform_url;
        var dsj = a.url_address.dsj_url || "https://adv2.hntengy.cn";
        let e = a.extraInfo;
        P8Log("开始获取服务器数据 " + getTime());
        P8Log("data_url=" + t + ", rg_url=" + n + ", platform_url=" + i + ", dsj_url=" + dsj);
        Object.assign(P8_QuickGame_Data, {
          data_url: t,
          rg_url: n,
          platform_url: i,
          dsj_url: dsj,
          extraInfo: e
        });
        P8OppoSDK.getMerchantData().then((e) => {
          console.log('[ getMerchantData ] >', e)
          let a = e.userInfo ? e.userInfo : null;
          if (!a) {
            resolve({
              result: 1,
              data: {
                msg: "获取oppo userInfo 数据失败"
              }
            })
            return;
          }

          let t = a.userId;
          P8_QuickGame_Data.playerId = t;
          P8_QuickGame_Data.device = t;
          let n = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
          let i = parseInt((new Date).getTime() / 1e3);
          var r = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${i}${i}`);
          let o = {
            channelid: P8_QuickGame_Data.channelid,
            aid: P8_QuickGame_Data.aid,
            site: P8_QuickGame_Data.site,
            channelUid: t,
            adid: t,
            udid: t,
            channelUserName: "",
            sign: r,
            time: i
          };
          P8Log("XmlHttpRequest  ********+ url=" + n + ", data==" + JSON.stringify(o));
          P8OppoSDK.XmlHttpRequest(n, "POST", o, e => {
            P8Log("play800登录返回 res=" + JSON.stringify(e));
            if (e.result == 0) {
              P8Log("获取uid成功~" + getTime());
              P8_QuickGame_Data.uid = e.data.uid;
              P8_QuickGame_Data.account = e.data.username;
              e.userInfo = a;
              console.log(" play800  log 2025 -02/13 login : ", JSON.stringify(e));
              resolve(e)
            } else {
              P8Log("获取aid失败~");
              resolve(e)
            }
          })
        })
      } else {
        let e = {
          result: 1,
          data: {
            msg: "获取域名数据失败"
          }
        };
        resolve(e);
        console.log("获取服务器域名失败")
      }
    })
  })

  return result;
}

P8OppoSDK.getMerchantData = function () {
  let e = new Promise((c, e) => {
    P8Log("获取服务器数据1");
    let t = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getconfig"
    };
    P8OppoSDK.XmlHttpRequest(t + "?data=" + JSON.stringify(a), "GET", null, a => {
      if (a.result == 0) {
        P8Log("获取服务参数成功" + getTime() + "传入参数:" + t + "?data=");
        let e = a.data[0];
        P8_QuickGame_Data.appKey = e.appkey;
        P8_QuickGame_Data.appSecret = e.appsecret;
        P8_QuickGame_Data.backcallUrl = e.backcallUrl;
        P8_QuickGame_Data.extraInfo = e.extraInfo;
        P8Log("术良data" + JSON.stringify(a));
        P8Log("术良res" + JSON.stringify(e));
        P8Log("appKey" + e.appkey);
        P8Log("appSecret" + e.appsecret);
        qg.login({
          success: function (e) {
            P8_QuickGame_Data.token = e.token;
            console.log("qg.login 返回的res", JSON.stringify(e));
            var a = e.data;
            let t = (new Date)
              .getTime();
            console.log(" P8_QuickGame_Data.appKey :", P8_QuickGame_Data.appKey);
            console.log(" P8_QuickGame_Data.appSecret :", P8_QuickGame_Data.appSecret);
            let n = "appKey=" + P8_QuickGame_Data.appKey + "&" + "appSecret=" + P8_QuickGame_Data.appSecret + "&" + "pkgName=" + P8_QuickGame_Data.pkgName + "&" + "timeStamp=" + t + "&" + "token=" + a.token;
            let i = hex_md5(n);
            i = i.toLocaleUpperCase();
            console.log("md5 要转换成大写:", i);
            let r = "https://play.open.oppomobile.com/instant-game-open/userInfo";
            let o = "pkgName=" + P8_QuickGame_Data.pkgName + "&timeStamp=" + t + "&token=" + a.token + "&sign=" + i + "&version=1.0.0";
            console.log("请求登陆的参数：" + JSON.stringify(o));
            P8OppoSDK.XmlHttpRequest(r + "?" + o, "GET", null, e => {
              console.log("返回的玩家信息:" + JSON.stringify(e));
              c(e)
            })
          },
          fail: function (e) {
            c(e);
            console.log("登陆失败数据", JSON.stringify(e))
          }
        })
      }
    })
  });
  return e
}

P8OppoSDK.pay = function (o) {
  let result = new Promise((resolve, reject) => {
    let c = (new Date).getTime();
    let t = {
      appId: P8_QuickGame_Data.appid,
      openId: P8_QuickGame_Data.token,
      timestamp: c,
      productName: o.productName,
      productDesc: o.productDesc,
      count: o.count,
      price: o.price,
      currency: "CNY",
      callBackUrl: P8_QuickGame_Data.backcallUrl,
      cpOrderId: o.cpOrderId,
      appVersion: o.appVersion,
      engineVersion: P8_QuickGame_Data.engineVersion,
      model: P8_QuickGame_Data.model,
      attach: o.attach ? o.attach : o.extraInfo
    };

    P8Log("执行pay 2025/05/15");
    P8Log("传入的支付参数:" + JSON.stringify(t));

    let p8PayTime = (new Date).getTime();
    let p8PaySign = hex_md5(`${P8_QuickGame_Data.site}${p8PayTime}${P8_QuickGame_Data.key}`);

    if (o.extraInfo && typeof o.extraInfo == "object") {
      let obj = JSON.parse(P8_QuickGame_Data.extraInfo);
      Object.assign(obj, o.extraInfo);
      P8_QuickGame_Data.extraInfo = JSON.stringify(obj);
      P8Log("融合后的2:" + P8_QuickGame_Data.extraInfo);
    }

    let p8PayParams = {
      serverId: o.serverId,
      username: o.userName,
      grade: o.grade,
      amount: o.price * o.count,
      desc: o.productDesc,
      productDesc: o.productDesc,
      productName: o.productName,
      orderId: o.cpOrderId,
      roleName: o.userName,
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      device: P8_QuickGame_Data.playerId,
      roleId: o.roleId,
      subject: P8_QuickGame_Data.appName,
      extraInfo: P8_QuickGame_Data.extraInfo,
      time: p8PayTime,
      sign: p8PaySign
    }

    P8OppoSDK.madeOrder(p8PayParams).then((res) => {
      if (res.result == 0) {
        P8Log(" P8下单成功: " + JSON.stringify(res));
        let a = {
          site: P8_QuickGame_Data.site,
          channel: P8_QuickGame_Data.channelid,
          version: "1.0",
          function: "getsign"
        };
        Object.assign(a, t);
        let r = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
        console.log("sign 请求参数:" + JSON.stringify(a));
        P8OppoSDK.XmlHttpRequest(r + "?data=" + JSON.stringify(a), "GET", null, a => {
          console.log("请求术良sign返回的是" + JSON.stringify(a));
          if (a.result == 0) {
            console.log("获取服务sign成功" + a.data.sign + getTime());
            t.sign = a.data.sign;
            console.log("支付请求参数 : ", JSON.stringify(t));
            let e = "https://jits.open.oppomobile.com/jitsopen/api/pay/v1.0/preOrder";
            P8OppoSDK.XmlHttpRequest(e, "POST", t, a => {
              console.log("oppo 支付返回的数据" + JSON.stringify(a));
              if (a.code == 200) {
                let t = a.data;
                let e = {
                  site: P8_QuickGame_Data.site,
                  channel: P8_QuickGame_Data.channelid,
                  version: "1.0",
                  function: "getsign",
                  appKey: P8_QuickGame_Data.appKey,
                  orderNo: t.orderNo,
                  timestamp: c
                };
                console.log("请求二次sign 请求参数:" + JSON.stringify(e));
                P8OppoSDK.XmlHttpRequest(r + "?data=" + JSON.stringify(e), "GET", null, a => {
                  console.log("请求术良二次sign返回的是" + JSON.stringify(a));
                  if (a.result == 0) {
                    console.log("获取 二次sign成功" + a.data.sign + getTime());
                    let e = a.data.sign;
                    qg.pay({
                      appId: P8_QuickGame_Data.appid,
                      token: P8_QuickGame_Data.token,
                      timestamp: c,
                      paySign: e,
                      orderNo: t.orderNo,
                      success: function (e) {
                        console.log("支付成功", JSON.stringify(e.data));
                        resolve(e)
                      },
                      fail: function (e) {
                        console.log("支付失败", JSON.stringify(e));
                        resolve(e)
                      }
                    })
                  } else {
                    console.log("二次获取失败")
                  }
                })
              } else {
                P8Log("下单没有拉起")
              }
            })
          }
        }, false)
      } else {
        P8Log(" P8下单失败: " + JSON.stringify(res));
      }
    })
  })

  return result;
}

P8OppoSDK.madeOrder = function (sdkParams) {
  P8Log(" P8下单参数: " + JSON.stringify(sdkParams));
  let result = new Promise((resolve, reject) => {
    let n = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
    P8OppoSDK.XmlHttpRequest(n, "POST", sdkParams, e => {
      console.log("p8调用下单返回的数据:" + JSON.stringify(e))
      resolve(e);
    })
  })
  return result
}

P8OppoSDK.adInitalization = function (e) {
  console.log("oppo 广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));
      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }
      // 销毁广告实例
      destroyVideoAd();
      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        adUnitId: adUnitId,
      });

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;

        let result = {
          result: 0,
          data: {
            msg: "激励视频 广告加载事件成功"
          }
        }

        if (e.success) e.success(result);
      });

      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;

        let result = {
          result: 1,
          data: {
            msg: "激励视频 广告加载异常",
            error: err
          }
        }

        if (e.fail) e.fail(result);
      });

      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          print("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          print("激励视频 初始加载失败", err);
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

P8OppoSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          print("激励视频 重试失败", err);
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

P8OppoSDK.XmlHttpRequest = function (e, a = "post", t = {}, n = null, i = true) {
  let r = new XMLHttpRequest;
  r.open(a, e);
  if (i) {
    r.setRequestHeader("Content-type", "application/json")
  } else {
    console.log("术良 url" + e);
    r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
  }
  r.send(JSON.stringify(t));
  r.onreadystatechange = function () {
    if (r.status == 200 && r.readyState == 4) {
      let e = JSON.parse(r.responseText);
      console.log("请求返回的数据是什么:" + JSON.stringify(e));
      if (n) {
        n(e)
      }
    }
  }
}

// ========================================================== oppo SDK end =================================================

// ========================================================== vivo SDK start =================================================

p8Vivosdk.Login = function () {
  let i = new Promise((c, e) => {
    let a = "https://center.play800.cn/fast_game_callback/getaliyuntxt";
    console.log("请求术良 vivo 数据");
    p8Vivosdk.XmlHttpRequest(a + "?url=https://ks3-cn-shanghai.ksyun.com/aliyun/936640176220913570.txt", "GET", null, a => {
      console.log("请求术良 vivo 数据" + advancedObjectToString(a));
      if (a.url_address) {
        var t = a.url_address.data_url;
        var n = a.url_address.rg_url;
        var i = a.url_address.platform_url;
        let dsj_url = a.url_address.dsj_url || 'https://adv2.hntengy.cn';
        let e = a.extInfo;
        P8Log("开始获取服务器数据 " + getTime());
        P8Log("data_url=" + t + ", rg_url=" + n + ", platform_url=" + i + ", dsj_url=" + dsj_url);
        Object.assign(P8_QuickGame_Data, {
          data_url: t,
          rg_url: n,
          platform_url: i,
          extInfo: e,
          dsj_url: dsj_url
        });
        p8Vivosdk.GetMerchantData().then(e => {
          let a = e ? e : null;
          if (!a) {
            c({
              result: 1,
              data: {
                msg: "获取vivo userInfo 数据失败"
              }
            })
          }
          let t = e.openId;
          P8_QuickGame_Data.playerId = t;
          P8_QuickGame_Data.device = t;
          let n = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
          let i = parseInt((new Date)
            .getTime() / 1e3);
          var r = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${i}${i}`);
          let o = {
            channelid: P8_QuickGame_Data.channelid,
            aid: P8_QuickGame_Data.aid,
            site: P8_QuickGame_Data.site,
            channelUid: t,
            adid: t,
            udid: t,
            channelUserName: "",
            sign: r,
            time: i
          };
          P8Log("XmlHttpRequest  2021+ url=" + n + ", data==" + JSON.stringify(o));
          p8Vivosdk.XmlHttpRequest(n, "POST", o, e => {
            P8Log("play800登录返回 res=" + JSON.stringify(e));
            if (e.result == 0) {
              P8Log("获取uid成功~" + getTime());
              P8_QuickGame_Data.uid = e.data.uid;
              e.vivo_data = a;
              console.log(" play800  log 2021 -06/30 login : ", JSON.stringify(e));
              c(e)
            } else {
              P8Log("获取aid失败~");
              c(e)
            }
          })
        })
      } else {
        let e = {
          result: 1,
          data: {
            msg: "获取数据失败"
          }
        };
        c(e);
        console.log("play800 init 获取服务器数据失败")
      }
    })
  });
  return i
}

p8Vivosdk.GetMerchantData = function () {
  let e = new Promise((c, e) => {
    P8Log("获取服务器数据1");
    let t = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getconfig"
    };
    console.log("test == P8_QuickGame_Data : ", advancedObjectToString(P8_QuickGame_Data), ":", advancedObjectToString(a));
    console.log("test == P8_QuickGame_Data : ", advancedObjectToString(a));
    console.log("test == P8_QuickGame_Data e: ", JSON.stringify(e));
    console.log('[ 1 ] >', 1)
    p8Vivosdk.XmlHttpRequest(t + "?data=" + JSON.stringify(a), "GET", null, a => {
      console.log('[ a ] >', advancedObjectToString(a))
      if (a.result == 0) {
        console.log('[ 2 ] >', 2)
        P8Log("获取服务参数成功" + getTime() + "传入参数:" + t + "?data=");
        let e = a.data[0];
        P8_QuickGame_Data.appKey = e.appkey;
        P8_QuickGame_Data.appSecret = e.appsecret;
        P8_QuickGame_Data.backcallUrl = e.backcallUrl;
        P8_QuickGame_Data.nonce = getNonce();
        //P8_QuickGame_Data.signature = e.signature;
        P8Log("术良data" + JSON.stringify(a));
        P8Log("术良res" + JSON.stringify(e));
        P8Log("appKey" + e.appkey);
        P8Log("appSecret" + e.appsecret);
        if (qg.getSystemInfoSync().platformVersionCode >= 1063) {
          qg.login({
            success: function (e) {
              P8_QuickGame_Data.token = e.token;
              console.log("qg.login 返回的res", JSON.stringify(e));
              var a = e.data;
              let t = (new Date).getTime();
              let n = "appKey=" + P8_QuickGame_Data.appKey + "&" + "appSecret=" + P8_QuickGame_Data.appSecret + "&" + "nonce=" + P8_QuickGame_Data.nonce + "&" + "pkgName=" + P8_QuickGame_Data.pkgName + "&" + "timestamp=" + t + "&" + "token=" + a.token;
              console.log(" P8_QuickGame_Data.nonce :", P8_QuickGame_Data.nonce);
              console.log("-------------加密前", n);
              let i = sha256_digest(n);
              i = i.toLocaleUpperCase();
              //console.log("md5 要转换成大写:", i);
              let r = "https://quickgame.vivo.com.cn/api/quickgame/cp/account/userInfo";
              let o = "nonce=" + P8_QuickGame_Data.nonce + "&pkgName=" + P8_QuickGame_Data.pkgName + "&timestamp=" + t + "&token=" + a.token + "&signature=" + i; //+ "&version=1.0.0";
              console.log("请求登陆的参数：" + JSON.stringify(o));
              p8Vivosdk.XmlHttpRequest(r + "?" + o, "GET", null, e => {
                console.log("返回的玩家信息:" + JSON.stringify(e.data));
                c(e.data)
              })
            },
            fail: function (e) {
              c(e);
              console.log("登陆失败数据", JSON.stringify(e))
            }
          })
        }
      } else {

      }
    })
  });
  return e
}

p8Vivosdk.pay = function (o) {
  console.log('[ vivo 进入支付 ] >', advancedObjectToString(o))
  let c = (new Date)
    .getTime();
  let time = dateFormat();
  let params = {
    appId: P8_QuickGame_Data.appid,
    cpOrderNumber: o.cpOrderId,
    productName: o.productName,
    productDesc: o.productDesc,
    orderAmount: o.price,
    notifyUrl: P8_QuickGame_Data.backcallUrl,
    expireTime: time,
    extInfo: o.extInfo ? o.extInfo : o.extraInfo,
  };
  P8Log("执行pay 2025");

  let a = new Promise(function (i, e) {
    let a = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "1.0",
      function: "getsign"
    };
    Object.assign(a, params);
    let r = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;
    console.log("sign 请求参数:" + advancedObjectToString(a));
    p8Vivosdk.XmlHttpRequest(r + "?data=" + JSON.stringify(a), "GET", null, a => {
      console.log("请求术良sign返回的是" + advancedObjectToString(a));
      if (a.result == 0) {
        console.log("获取服务sign成功" + a.data.sign + getTime());
        params.vivoSignature = a.data.sign;
        let Object_vivo = {
          code: '',
          msg: ''
        }
        console.log("vivo 支付返回的数据" + advancedObjectToString(a));

        var e = hex_md5(`${P8_QuickGame_Data.site}${c}${P8_QuickGame_Data.key}`);

        if (o.extraInfo && typeof o.extraInfo == "object") {
          let obj = JSON.parse(P8_QuickGame_Data.extraInfo);
          Object.assign(obj, o.extraInfo);
          P8_QuickGame_Data.extraInfo = JSON.stringify(obj);
          P8Log("融合后的2:" + P8_QuickGame_Data.extraInfo);
        }

        let t = {
          serverId: o.serverId,
          username: o.userName,
          grade: o.grade,
          amount: o.price * o.count,
          desc: o.productDesc,
          productDesc: o.productDesc,
          productName: o.productName,
          orderId: o.cpOrderId,
          roleName: o.userName,
          aid: P8_QuickGame_Data.aid,
          uid: P8_QuickGame_Data.uid,
          site: P8_QuickGame_Data.site,
          channel: P8_QuickGame_Data.channelid,
          device: P8_QuickGame_Data.playerId,
          roleId: o.roleId,
          subject: P8_QuickGame_Data.appName,
          extraInfo: P8_QuickGame_Data.extraInfo,
          time: c,
          sign: e
        };
        P8Log(" 2025 支付8001 playerId : " + P8_QuickGame_Data.playerId);
        P8Log(" 2025 支付8001 appName : " + P8_QuickGame_Data.appName);
        P8Log(" 2025 支付8001 extraInfo : " + advancedObjectToString(P8_QuickGame_Data.extraInfo));
        P8Log(" 2025 支付8001 sdkParams : " + advancedObjectToString(t));
        let n = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
        p8Vivosdk.XmlHttpRequest(n, "POST", t, e => {
          console.log("p8这边调用下单参数:" + advancedObjectToString(e))
          if (e.result == 0) {
            qg.pay({
              orderInfo: JSON.stringify(params),
              success: function (ret) {
                console.log("支付成功", advancedObjectToString(ret));
                Object_vivo.code = 0;
                Object_vivo.msg = "支付成功";
                i(Object_vivo)
              },
              fail: function (err) {
                console.log("支付失败" + advancedObjectToString(err));
                Object_vivo.code = -2;
                Object_vivo.msg = "支付失败：" + advancedObjectToString(err);
                i(Object_vivo)
              },
              cancel: function (ret) {
                console.log("支付取消" + advancedObjectToString(ret))
                Object_vivo.code = -1;
                Object_vivo.msg = "支付取消";
                i(Object_vivo)
              },
              complete: function () {
                console.log("支付完成")
              }
            })
          } else {
            console.log("P8SDK下单失败");
          }
        })
      }
    }, false)
  });
  return a
}

p8Vivosdk.adInitalization = function (e) {
  console.log("vivo 广告初始化", JSON.stringify(e));
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));
      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }
      // 销毁广告实例
      destroyVideoAd();
      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        posId: adUnitId,
      });

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        console.log("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;

        let result = {
          result: 0,
          data: {
            msg: "激励视频 广告加载事件成功"
          }
        }

        if (e.success) e.success(result);

      });

      qg_videoAD.onError((err) => {
        console.log("激励视频 广告加载异常", JSON.stringify(err));
        adLoadState.isLoading = false;
        adLoadState.isReady = false;

        let result = {
          result: 1,
          data: {
            msg: "激励视频 广告加载异常",
            error: err
          }
        }

        if (e.fail) e.fail(result);
      });

      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          console.log("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          console.log("激励视频 初始加载失败", JSON.stringify(err));
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

p8Vivosdk.videoADShow = function (t, a, c, onShow, adp) {
  console.log("vivo 广告展示");
  // 检查是否在冷却时间内
  const now = Date.now();
  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    console.log("广告展示太频繁，请稍后再试");
    return;
  }
  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    console.log("广告正在展示中");
    return;
  }
  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    console.log("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();
  // 设置广告正在展示状态
  isVideoShowInProgress = true;
  // 重置状态锁
  isVideoLogReported = false;
  // 更新计时值
  lastVideoShowTime = now;
  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      console.log("激励视频 广告加载异常", JSON.stringify(e));
      isVideoShowInProgress = false; // 重置状态
      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          console.log("激励视频 重试失败", JSON.stringify(err));
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;
  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    isVideoLogReported = true;
  }
  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态
    let arg = {
      type: "RewardedVideoAd",
      status: "0",
      geType: "reward",
    }
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)

    if ((e && e.isEnded) || e === undefined) {
      console.log("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }
      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      setTimeout(() => {
        debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)
      }, 1000)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }
      if (t) t()
    } else {
      console.log("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      console.log("激励视频 加载失败", JSON.stringify(err));
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

p8Vivosdk.XmlHttpRequest = function (e, a = "post", t = {}, n = null, i = true) {
  let r = new XMLHttpRequest;
  r.open(a, e);
  if (i) {
    r.setRequestHeader("Content-type", "application/json")
  } else {
    console.log("术良 url" + e);
    r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
  }
  r.send(JSON.stringify(t));

  r.onreadystatechange = function () {
    console.log("-----------------测试", r.status, r.readyState);
    if (r.status == 200 && r.readyState == 4) {
      let e = JSON.parse(r.responseText);
      console.log("请求返回的数据是什么:" + JSON.stringify(e));
      if (n) {
        n(e)
      }
    }
  }
}

p8Vivosdk.qgRequest = function (e, t = "post", a = {}, s = null, f = null) {
  qg.request({
    url: e,
    method: t,
    data: a,
    dataType: "json",
    success: (success) => {
      if (s) {
        s(success);
      }
    },
    fail: (fail) => {
      if (f) {
        f(fail);
      }
    },
  });
}

// ========================================================== vivo SDK end =================================================

// ========================================================== 荣耀 SDK start =================================================

P8HonorSDK.Login = async function (a) {
  let result = new Promise(async (resolve, reject) => {
    try {
      const urlConfigRes = await P8HonorSDK.getUrlConfig();
      console.log('[ 域名初始化成功 ] >', JSON.stringify(urlConfigRes));

      if (urlConfigRes.result !== 0) {
        resolve(urlConfigRes);
        return;
      }

      qg.login({
        needAuthCode: true,
        success: async (res) => {
          console.log('[ 荣耀获取antuCode成功 ] >', JSON.stringify(res));
          const {
            authCode,
            openId,
            nickname,
            avatarUrl,
            unionId
          } = res;
          P8_QuickGame_Data.openid = openId;
          P8_QuickGame_Data.device = openId;
          P8_QuickGame_Data.nickname = nickname;
          P8_QuickGame_Data.avatarUrl = avatarUrl;
          P8_QuickGame_Data.unionid = unionId;
          P8_QuickGame_Data.device_code = openId;
          // 荣耀登录
          let time = parseInt(new Date().getTime() / 1e3);
          let url = `${P8_QuickGame_Data.platform_url}/mGame/login/${P8_QuickGame_Data.channelid}`
          let login_data = {
            site: P8_QuickGame_Data.site,
            appid: P8_QuickGame_Data.appid,
            js_code: authCode,
            openid: P8_QuickGame_Data.openid,
            unionid: P8_QuickGame_Data.unionid,
            aid: P8_QuickGame_Data.aid,
            code: queryData.code,
            channel_parame: start_param,
            time: time,
          }
          ChangeUndefined(login_data);
          let n = newSignGetType_log(login_data);
          console.log('[ n ] >', n)
          let d = hex_md5(n);
          login_data.sign = d;
          console.log('[ play800登录请求参数 ] >', JSON.stringify(login_data));
          httpRequest({
            url: url,
            method: 'POST',
            data: login_data,
          }).then(response => {
            console.log(' [ play800登录返回 ] > ', JSON.stringify(response));
            if (response.result == 0) {
              let e = response.data;
              P8_QuickGame_Data.uid = e.uid + '';
              P8_QuickGame_Data.account = e.account;
              P8_QuickGame_Data.password = e.password;
              P8_QuickGame_Data.sessionid = e.sessionid;
              P8_QuickGame_Data.istemp = e.istemp;
              P8_QuickGame_Data.sessiontime = e.sessiontime;
              print("登录成功回调 进行赋值", JSON.stringify(P8_QuickGame_Data));
              let cp = {
                result: 0,
                data: {
                  openid: P8_QuickGame_Data.openid,
                  uid: P8_QuickGame_Data.uid,
                  account: P8_QuickGame_Data.account,
                  password: P8_QuickGame_Data.password,
                  sessionid: e.sessionid,
                  sessiontime: e.sessiontime,
                }
              }
              Object.assign(cp.data, e);
              loginResult = cp;
              resolve(loginResult);
              P8HonorSDK.getPayConfig();
            } else {
              print("play800登录异常:" + JSON.stringify(response));
              resolve(response);
            }
          }).catch(error => {
            console.error('登录失败', JSON.stringify(error));
            reject(error);
          });
        },
        fail: (err) => {
          console.log('[ 荣耀获取antuCode失败 ] >', JSON.stringify(err));
          resolve(err);
        }
      });
    } catch (error) {
      reject(error);
    }
  });

  return result;
}

P8HonorSDK.getUrlConfig = function () {
  console.log('[ getUrlConfig ] >', )
  let p = new Promise((resolve, reject) => {
    let url = `https://ksyun.oss-cn-hangzhou.aliyuncs.com/${P8_QuickGame_Data.aid}.txt`;
    httpRequest({
      url: url,
      method: 'GET',
    }).then(response => {
      console.log('域名请求成功', JSON.stringify(response));
      let config = response.data || response;
      console.log('[ config ] >', JSON.stringify(config))
      if (config.url_address) {
        let data_url = config.url_address.data_url;
        let rg_url = config.url_address.rg_url;
        let platform_url = config.url_address.platform_url;
        let dsj_url = config.url_address.dsj_url;
        console.log("data_url=" + data_url + ", rg_url=" + rg_url + ", platform_url=" + platform_url + ", dsj_url=" + dsj_url);

        Object.assign(P8_QuickGame_Data, {
          data_url: data_url, // 上报url
          rg_url: rg_url, //// 支付url
          platform_url: platform_url,
          dsj_url: dsj_url,
        });

        var response = {
          result: 0,
          data: {
            msg: "域名初始化成功"
          }
        };

      } else {
        var response = {
          result: 1,
          data: {
            msg: "域名获取失败，请联系运营配置"
          }
        };
      }
      resolve(response);
    }).catch(error => {
      console.error('域名获取失败', JSON.stringify(error));
      reject(error);
    });
  })

  return p;
}

P8HonorSDK.getPayConfig = function () {
  console.log('[ P8HonorSDK getPayConfig] >', )
  let p = new Promise((resolve, reject) => {
    let url = `${P8_QuickGame_Data.platform_url}/mGame/payConf/${P8_QuickGame_Data.channelid}`
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      time: time,
    }
    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付配置参数请求 ] >', JSON.stringify(pay_data));
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(res => {
      console.log(' [ play800支付配置参数返回 ] > ', JSON.stringify(res));
      if (res.result == 0 && res.data.is_set == true) {
        P8_QuickGame_Data.CPid = res.data.CPid;
        P8_QuickGame_Data.publicKey = res.data.publicKey;
        resolve();
      } else if (res.result == 0 && res.data.is_set == false) {
        console.log("支付配置参数未设置");
        resolve();
      } else if (res.result == 1) {
        console.log("支付配置参数接口请求失败");
      }
    }).catch(error => {
      console.error('支付配置参数接口请求失败', JSON.stringify(error));
      reject(error);
    });
  });
  return p;
}

P8HonorSDK.pay = async function (a) {
  let result = new Promise((resolve, reject) => {
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      cp_order_id: a.cp_order_id ? a.cp_order_id : a.cpOrderId,
      roleid: a.roleid ? a.roleid : a.roleId,
      rolename: a.rolename ? a.rolename : a.userName,
      level: a.level ? a.level : a.grade,
      serverid: a.serverid ? a.serverid : a.serverId,
      productid: a.productid ? a.productid : a.productId,
      product_name: a.product_name ? a.product_name : a.productName,
      money: a.money ? a.money : a.price / 100,
      ext: a.ext ? a.ext : a.extraInfo,
      time: time,
      ip: a.ip,
      udid: P8_QuickGame_Data.openid,
      test: a.test ? a.test : '0',
    }

    if (pay_data.ext && typeof pay_data.ext == 'object') {
      pay_data.ext = JSON.stringify(pay_data.ext);
    }

    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付请求参数 ] >', JSON.stringify(pay_data));
    let url = `${P8_QuickGame_Data.rg_url}/mini/order/rongyao`
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(res => {
      console.log(' [ play800支付返回 ] > ', JSON.stringify(res));
      if (res.result == 0) {
        const orderInfo = {
          appId: P8_QuickGame_Data.appid,
          cpId: P8_QuickGame_Data.CPid,
          productId: pay_data.productid,
          publicKey: P8_QuickGame_Data.publicKey,
          productName: pay_data.product_name,
          productDesc: pay_data.product_name,
          orderAmount: Number(pay_data.money) * 100,
          developerPayload: res.data.order_id,
          bizOrderNo: res.data.order_id,
          needSandboxTest: a.needSandboxTest ? a.needSandboxTest : 0,
        }
        console.log('[ 荣耀支付请求参数 ] >', JSON.stringify(orderInfo));
        qg.pay({
          orderInfo,
          productInfo: orderInfo,
          success: function (ret) {
            console.log('支付成功: ' + JSON.stringify(ret))
            let res = {
              result: 0,
              data: {
                msg: "支付成功",
                data: ret
              }
            }
            resolve(res);
          },
          fail: function (err) {
            console.log('支付失败: ' + JSON.stringify(err))
            let res = {
              result: 1,
              data: {
                msg: "支付失败",
                data: err
              }
            }
            resolve(res);
          },
          cancel: function (ret) {
            console.log('支付取消: ' + JSON.stringify(ret))
            let res = {
              result: 1,
              data: {
                msg: "支付取消",
                data: ret
              }
            }
            resolve(res);
          },
        })
      } else {
        print("play800支付异常:" + JSON.stringify(res));
        resolve(res);
      }
    }).catch(error => {
      console.error('支付异常', JSON.stringify(error));
      reject(error);
    });
  });

  return result;
}

P8HonorSDK.adInitalization = function (e) {
  console.log("荣耀广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));

      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }

      // 销毁广告实例
      destroyVideoAd();

      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        adUnitId: adUnitId,
      });

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;

        let result = {
          result: 0,
          data: {
            msg: "激励视频 广告加载事件成功"
          }
        }

        if (e.success) e.success(result);
      });

      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;

        let result = {
          result: 1,
          data: {
            msg: "激励视频 广告加载异常",
            error: err
          }
        }

        if (e.fail) e.fail(result);
      });

      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          print("激励视频 广告加载成功");
          adLoadState.isReady = true;
        }).catch(err => {
          print("激励视频 初始加载失败", err);
          adLoadState.isReady = false;
        }).finally(() => {
          adLoadState.isLoading = false;
        });
      }
    }
  }
};

P8HonorSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调
    }).catch((e) => {
      print("激励视频 广告加载异常", e);
      isVideoShowInProgress = false; // 重置状态

      // 展示失败时尝试重新加载并展示
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().then(() => {
          return qg_videoAD.show().then(() => {
            if (onShow) onShow(); // 重试成功后也要触发回调
          });
        }).catch((err) => {
          print("激励视频 重试失败", err);
          if (c) c(err);
        }).finally(() => {
          adLoadState.isLoading = false;
          isVideoShowInProgress = false;
        });
      } else {
        if (c) c(e);
      }
    });
  };

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      // 预加载下一个广告
      if (!adLoadState.isLoading) {
        adLoadState.isLoading = true;
        qg_videoAD.load().finally(() => {
          adLoadState.isLoading = false;
        });
      }

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }
  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

// ========================================================== 荣耀 SDK end =================================================

// ========================================================== 华为RunTime SDK start ===============================================

P8HuaweiRunTimeSDK.RunTimeInit = function () {
  console.log("华为RunTime初始化");
  let p = new Promise((resolve, reject) => {
    console.log("请求服务器");
    httpRequest({
      url: `https://ksyun.oss-cn-hangzhou.aliyuncs.com/${P8_QuickGame_Data.aid}.txt`,
      method: "GET",
      data: {},
    }).then((data) => {
      console.log("请求服务器数据" + JSON.stringify(data));
      data = data.data || data;
      if (data.url_address) {
        var data_url = data.url_address.data_url;
        var rg_url = data.url_address.rg_url;
        var platform_url = data.url_address.platform_url;
        P8Log("开始获取服务器数据 " + getTime());

        P8Log("data_url=" + data_url + ", rg_url=" + rg_url + ", platform_url=" + platform_url);
        Object.assign(P8_QuickGame_Data, {
          data_url: data_url, // 上报url
          rg_url: rg_url, //// 支付url
          platform_url: platform_url
        });

        P8HuaweiRunTimeSDK.getMerchantData();
        var data = {
          result: 0,
          data: {
            msg: "初始化成功"
          }
        };
      } else {
        var data = {
          result: 1,
          data: {
            msg: "域名获取失败，请联系运营配置"
          }
        };
      }
      resolve(data);
    }).catch((err) => {
      console.log("请求服务器失败" + JSON.stringify(err));
      reject(err);
    })

  });

  return p;
}

P8HuaweiRunTimeSDK.getMerchantData = function () {
  P8Log("获取服务器数据1");

  let url = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
  // let url = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;

  // 配置一下
  let data = {
    site: P8_QuickGame_Data.site,
    channel: P8_QuickGame_Data.channelid,
    version: "2.0",
    function: "getconfig"
  };
  httpRequest({
    url: url + "?data=" + JSON.stringify(data),
    method: "GET",
    data: null,
  }).then((data) => {
    if (data.result == 0) {
      // 获取参数成功
      P8Log("获取服务参数成功" + getTime() + "传入参数:" + url + "?data=");
      let res = data.data[0];
      P8_QuickGame_Data.merchantId = res.merchantId;
      P8_QuickGame_Data.extraInfo = res.extraInfo;
      P8_QuickGame_Data.merchantName = res.merchantName;
      P8_QuickGame_Data.publicKey = res.publicKey;
      P8_QuickGame_Data.backcallUrl = res.backcallUrl;

      P8Log("data" + data);
      P8Log("res" + res);
      P8Log("merchantId" + res.merchantId);
      P8Log("extraInfo" + res.extraInfo);
      P8Log("merchantName" + res.merchantName);
      P8Log("publicKey" + res.publicKey);
      P8Log("backcallUrl" + res.backcallUrl);

      P8Log("merchantId" + P8_QuickGame_Data.merchantId);
      P8Log("extraInfo" + P8_QuickGame_Data.extraInfo);
      P8Log("merchantName" + P8_QuickGame_Data.merchantName);
      P8Log("publicKey" + P8_QuickGame_Data.publicKey);
      P8Log("backcallUrl" + P8_QuickGame_Data.backcallUrl);

      P8HuaweiRunTimeSDK.searchUserPurchase().then((res) => {
        P8HuaweiRunTimeSDK.costConsumeOwnPurchase();
      });
    }
  }).catch((err) => {
    P8Log("请求服务器失败" + JSON.stringify(err));
    reject(err);
  })
}

P8HuaweiRunTimeSDK.searchUserPurchase = function () {
  P8Log('进入查询用户已购买的商品');
  let e = new Promise((resolve, reject) => {
    qg.obtainOwnedPurchases({
      ownedPurchasesReq: {
        priceType: 0,
        applicationID: P8_QuickGame_Data.appid,
        publicKey: P8_QuickGame_Data.publicKey,
      },
      success: function (str) {
        P8Log('查询用户已购买的商品数据返回结果:' + JSON.stringify(str))
        let appData = str
        if (appData.inAppPurchaseDataList) {
          huaweiRunTimeData.inAppPurchaseData = appData.inAppPurchaseDataList
          resolve(huaweiRunTimeData.inAppPurchaseData);
        } else {
          P8Log('查询用户已购买的商品数据返回结果为空')
          huaweiRunTimeData.inAppPurchaseData = []
          resolve(huaweiRunTimeData.inAppPurchaseData);
        }
      },
      fail: function (data, code) {
        console.log("obtainOwnedPurchases fail data =" + data, "code =" + code);
        P8Log('查询用户已购买的商品数据失败:' + JSON.stringify(data))
        reject(data);
      }
    })
  })
  return e;

}

P8HuaweiRunTimeSDK.costConsumeOwnPurchase = function () {
  let inAppPurchaseData = huaweiRunTimeData.inAppPurchaseData
  P8Log('进入消耗商品', JSON.stringify(inAppPurchaseData))

  if (!inAppPurchaseData || !Array.isArray(inAppPurchaseData) || inAppPurchaseData.length === 0) {
    P8Log("没有需要消耗的商品");
    return;
  }

  inAppPurchaseData.forEach(purchaseItem => {
    let purchaseToken = purchaseItem.purchaseToken
    let developerPayload = purchaseItem.developerPayload
    try {
      if (!purchaseToken || !developerPayload) {
        P8Log(`警告：商品数据不完整，缺失 purchaseToken 或 developerPayload: ${JSON.stringify(purchaseItem)}`);
        return;
      }
      qg.consumeOwnedPurchase({
        consumeOwnedPurchaseReq: {
          applicationID: P8_QuickGame_Data.appid,
          developerPayload: developerPayload,
          purchaseToken: purchaseToken,
          publicKey: P8_QuickGame_Data.publicKey
        },
        success: function (data) {
          P8Log('商品消耗返回结果:' + JSON.stringify(data))
          if (data.returnCode == 0) {
            P8Log("商品消耗成功");
          } else if (data.returnCode == 60053) {
            P8Log("商品已经消耗，不能再次消耗");
          } else {
            P8Log("商品消耗失败");
          }
        },
        fail: function (data, code) {
          console.log("consumeOwnedPurchase fail data =" + JSON.stringify(data), "code =" + code);
          P8Log('商品消耗失败返回结果:' + JSON.stringify(data))
        }
      })
    } catch (err) {
      P8Log(`解析商品数据出错: ${err.message}, 数据: ${purchaseItem}`);
    }
  })
}

P8HuaweiRunTimeSDK.Login = function () {
  console.log("华为RunTime登录");

  let e = new Promise((resolve, reject) => {
    var params = {
      appid: P8_QuickGame_Data.appid + "",
      forceLogin: 1
    };
    let loginArg = JSON.stringify(params);
    P8Log("正在登录华为账号 GameLogin, 参数是====>" + loginArg);
    qg.gameLoginWithReal({
      forceLogin: params.forceLogin,
      appid: params.appid,
      success: function (str) {
        P8Log("登录华为账号结果qg.onGameLoginWithRealResult, str=" + JSON.stringify(str));
        huaweiRunTimeData.isInGame = true; // 记录下 已经进入游戏了
        P8Log("华为账号登录成功 str=" + JSON.stringify(str));
        let gameUserData = str; //登录成功后会返回的用户数据
        let openid = gameUserData.playerId;
        P8_QuickGame_Data.rolename = gameUserData.displayName; // 玩家名字
        P8_QuickGame_Data.device = openid;
        P8_QuickGame_Data.device_type = openid;
        P8_QuickGame_Data.level = gameUserData.playerLevel; // 玩家等级
        P8_QuickGame_Data.playerId = openid;

        let url = `${P8_QuickGame_Data.platform_url}/api/createChannelUser`;
        let Md5_time = parseInt(new Date().getTime() / 1000);

        var Md5_sign = hex_md5(`${P8_QuickGame_Data.key}WX${P8_QuickGame_Data.site}WX${Md5_time}${Md5_time}`);
        let login_args = {
          channelid: P8_QuickGame_Data.channelid, // 渠道id
          aid: P8_QuickGame_Data.aid, // play800提供的aid
          site: P8_QuickGame_Data.site,
          channelUid: openid, // 渠道openid
          adid: openid, // 设备号
          udid: openid, // 设备号
          // 找不到可传空字符串
          channelUserName: "", // 渠道帐号

          sign: Md5_sign,
          time: Md5_time
        };

        var checkOverIndex = 0;
        var maxOverIndex = 1;
        var outResult = {};

        function checkBothOver() {
          if (maxOverIndex == checkOverIndex) {
            console.log(' 检测----------->3' + JSON.stringify(outResult));
            resolve(outResult);
          } else {
            console.log(' 还不行...', maxOverIndex, ":", checkOverIndex);
          }
        }

        console.log(' login_args ======>', JSON.stringify(login_args))

        httpRequest({
          url: url,
          method: "POST",
          data: login_args,
        }).then((res) => {
          P8Log("play800登录返回 res=" + JSON.stringify(res));
          checkOverIndex++;
          if (res.result == 0) {
            P8Log("获取uid成功~" + getTime());
            P8_QuickGame_Data.uid = res.data.uid;
            res.gameUserData = gameUserData;
            res.msg = '登录成功'
            console.log(" p8play log 2025 -05/30 login : ", JSON.stringify(res));
            outResult = res;
            // resolve(res);
            console.log(' 检测----------->1');
            checkBothOver();

            P8HuaweiRunTimeSDK.searchUserPurchase().then((res) => {
              P8HuaweiRunTimeSDK.costConsumeOwnPurchase();
            });

          } else {
            P8Log("获取aid失败~");
            res.msg = '登录失败'
            resolve(res);
          }
        }).catch((err) => {
          console.log("华为RunTime登录失败" + JSON.stringify(err));
          reject(err);
        });
      },
      fail: function (data, code) {
        console.log("game login with real fail:" + data + ", code:" + code);
        if (code == 7004 || code == 2012) {
          //取消登录
          let res = {
            result: 1,
            data: {
              msg: "取消登录",
              code: code,
              data: data
            }
          };
          resolve(res);
        } else {
          if (code == -1) {
            P8Log(" 出现此错误，表示鉴权失败。如果调试时使用打包的正式版本，版本中使用的指纹证书与华为开发者联盟配置的指纹证书保持一致。");
          } else if (code == 6004) {
            P8Log(
              " 出现此错误，请检查如下几点：\n1.(后台)是否已经申请开通游戏服务和开通帐号服务 。\n 2.登录传入的参数appid是否和华为开发者联盟获取的保持一致。获取方式请参见“创建快游戏”中的“获取APP ID”章节。\n3.rpk包使用的指纹证书是否和配置帐号服务时填写的一致。\n如果以上配置没有问题，可能是网关权限有延迟，请在AppGallery Connect多次尝试关闭再重新打开帐号服务和游戏服务的开关，并清空 HMS Core 的数据。。"
            );
          } else if (code == 7001) {
            P8Log(" 出现此错误，如果调试手机没有登录华为帐号，而登录参数forceLogin设置为0，则不会拉起登录界面，从而导致提示登录异常。出现此错误，请检查手机是否未登录华为帐号，并且forceLogin设置为0。");
          } else if (code == 7005) {
            P8Log(" 7005 错误码表示传入参数有误，请检查登录接口传入参数是否做了JSON.stringify(param)转换");
          } else {
            P8Log("登录异常 code：" + code + "data: " + JSON.stringify(str));
          }
          let res = {
            result: -1,
            data: {
              msg: "登录失败",
              code: code,
              data: data
            }
          };
          resolve(res);
        }
      }
    });
  })

  return e;
}

P8HuaweiRunTimeSDK.pay = function (params) {
  P8Log('进入华为支付', advancedObjectToString(params))

  let e = new Promise((resolve, reject) => {
    let data2 = {
      site: P8_QuickGame_Data.site,
      channel: P8_QuickGame_Data.channelid,
      version: "2.0",
      function: "getsign",
      //
      amount: Number(params.price), //商品金额
      applicationID: P8_QuickGame_Data.appid, //应用ID，在华为开发者联盟上获取的APP ID。
      country: "CN",
      currency: "CNY",
      url: P8_QuickGame_Data.backcallUrl,
      productDesc: params.productDesc,
      productName: params.productName,
      requestId: params.cpOrderId,
      merchantId: P8_QuickGame_Data.merchantId,
      urlver: 2,
      sdkChannel: 3
    };

    let signurl = `${P8_QuickGame_Data.platform_url}/fast_game_callback/getchannelother`;
    // let signurl = `https://tcenter.play800.cn/fast_game_callback/getchannelother`;
    console.log("sign 请求参数:" + JSON.stringify(data2));

    httpRequest({
      url: signurl + "?data=" + JSON.stringify(data2),
      method: "GET",
      data: {},
    }).then((data) => {
      if (data.result == 0) {
        // 获取参数成功
        console.log("获取服务参数2成功", JSON.stringify(data));
        P8_QuickGame_Data.sign = data.data.sign;
        console.log("开始拉起华为支付~" + getTime() + ":sign：" + P8_QuickGame_Data.sign);

        let Md5_time = parseInt(new Date().getTime() / 1000);
        let beforeSign = `${P8_QuickGame_Data.site}${Md5_time}${P8_QuickGame_Data.key}`;
        console.log('beforeSign ======》', beforeSign)
        let Md5_sign = hex_md5(beforeSign);

        if (params.extraInfo && typeof params.extraInfo == "object") {
          let obj = JSON.parse(P8_QuickGame_Data.extraInfo);
          Object.assign(obj, params.extraInfo);
          P8_QuickGame_Data.extraInfo = JSON.stringify(obj);
          P8Log("融合后的2:" + P8_QuickGame_Data.extraInfo);
        }

        let sdkParams = {
          // cp传入
          serverId: params.serverId,
          username: params.userName,
          grade: params.grade,
          amount: Number(params.price),
          desc: params.productDesc,
          productDesc: params.productDesc,
          productName: params.productName,
          orderId: params.cpOrderId,

          //
          roleName: params.userName,
          // config数据
          aid: P8_QuickGame_Data.aid,
          uid: P8_QuickGame_Data.uid,

          site: P8_QuickGame_Data.site,
          channel: P8_QuickGame_Data.channelid,
          device: P8_QuickGame_Data.playerId,
          roleId: params.roleId || P8_QuickGame_Data.playerId,
          subject: P8_QuickGame_Data.appName,
          extraInfo: P8_QuickGame_Data.extraInfo, //{"payCall":”https://qmzgh5gm.gameserver.youxigu.com/gmsys/h5/huaweiquickgame/paycallback.php”},
          time: Md5_time,

          sign: Md5_sign
        };

        console.log("华为支付请求参数 ======>", JSON.stringify(sdkParams));
        // P8下单成功后才拉起华为支付
        P8HuaweiRunTimeSDK.madeOrder(sdkParams).then((res) => {
          if (res.result == 0) {
            P8Log(" P8下单成功: " + JSON.stringify(res));
            // 华为3.0支付参数
            qg.createPurchaseIntent({
              purchaseIntentReq: {
                // 替换成真实有效的APP ID
                applicationID: P8_QuickGame_Data.appid,
                // 替换成AGC控制台的消耗型商品ID
                productId: params.productId,
                priceType: 0,
                developerPayload: params.cpOrderId,
                // 替换成真实有效的支付公钥
                publicKey: P8_QuickGame_Data.publicKey
              },
              success: function (data) {
                inAppPurchaseData = data;
                console.log("createPurchaseIntent success =" + JSON.stringify(data));
                console.log(" 3.0华为支付结果成功~ ", JSON.stringify(data));
                P8Log("支付结果: " + data.returnCode + ",message: " + data.returnCode);

                if (data.returnCode == 0) {
                  //支付成功       
                  let res = {
                    result: 0,
                    data: {
                      msg: "支付成功！",
                      code: data.returnCode
                    },
                  }
                  P8Log("支付成功！");

                  P8HuaweiRunTimeSDK.searchUserPurchase().then((res) => {
                    P8HuaweiRunTimeSDK.costConsumeOwnPurchase();
                  });

                  resolve(res);
                } else {
                  //支付失败
                  let res = {
                    result: 1,
                    data: {
                      msg: "支付失败！",
                      data: JSON.stringify(data),
                      code: data.returnCode
                    },
                  };
                  P8Log("支付失败", JSON.stringify(data));
                  resolve(res);
                }
              },
              fail: function (data, code) {
                console.log("createPurchaseIntent fail data =" + data, "code =" + code);
                console.log(" 3.0华为查询支付结果失败~ ", JSON.stringify(data));

                if (code == 60051) {
                  P8Log("出现此错误、由于已经拥有该商品，购买失败，未进行商品消耗");
                } else if (code == 60000) {
                  P8Log("用户取消支付");
                } else {
                  P8Log("支付失败");
                }

                let res = {
                  result: 1,
                  data: {
                    msg: "支付失败！",
                    data: JSON.stringify(data),
                    code: code
                  },
                };
                P8Log("支付失败", JSON.stringify(data));
                resolve(res);
              }
            })
          } else {
            P8Log(" P8下单失败: " + JSON.stringify(res));
          }
        })
      } else {
        P8Log("获取服务参数2失败", JSON.stringify(data));
      }
    }).catch((err) => {
      console.log("获取服务参数2失败", JSON.stringify(err));
      reject(err);
    });
  })

  return e;
}

P8HuaweiRunTimeSDK.madeOrder = function (sdkParams) {
  P8Log(" P8下单参数: " + JSON.stringify(sdkParams));
  let e = new Promise((resolve, reject) => {
    let url = `${P8_QuickGame_Data.platform_url}/sdk/createorder`;
    httpRequest({
      url: url,
      method: "POST",
      data: sdkParams,
    }).then((res) => {
      console.log("p8调用下单返回结果:" + JSON.stringify(res));
      resolve(res);
    }).catch((err) => {
      console.log("p8调用下单失败:" + JSON.stringify(err));
      reject(err);
    });
  })
  return e;
}

P8HuaweiRunTimeSDK.adInitalization = function (e) {
  console.log("华为Runtime 广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));

      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }

      // 销毁广告实例
      destroyVideoAd();

      // 创建广告实例
      qg_videoAD = qg.createRewardedVideoAd({
        adUnitId: adUnitId,
      });

      // 重置状态
      isVideoShowInProgress = false;

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功 initalization");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;

        let result = {
          result: 0,
          data: {
            msg: "激励视频 广告加载事件成功"
          }
        }

        if (e.success) e.success(result);
      });

      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常 initalization", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;
        isVideoShowInProgress = false;

        let result = {
          result: 1,
          data: {
            msg: "激励视频 广告加载异常",
            error: err
          }
        }

        if (e.fail) e.fail(result);
      });

      // 初始加载
      if (!adLoadState.isLoading && !adLoadState.isReady) {
        adLoadState.isLoading = true;
        qg_videoAD.load();
        adLoadState.isReady = true;
      }
    }
  }
};

P8HuaweiRunTimeSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内

  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }

  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show()
    if (onShow) onShow(); // 广告成功展示时触发回调
  };

  qg_videoAD.onError((err) => {
    print("激励视频 广告加载异常 showAd", err);
    adLoadState.isLoading = false;
    adLoadState.isReady = false;
    isVideoShowInProgress = false;
    if (c) c(err);
  });

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();
  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }
  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }
  const i = (e) => {
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isEnded) || e === undefined) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }

    // 预加载下一个广告
    if (!adLoadState.isLoading) {
      adLoadState.isLoading = true;
      qg_videoAD.load()
      adLoadState.isLoading = false;
    }
  };
  // 添加事件监听器
  qg_videoAD.onClose(i);
  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load();
    showAd();
  } else {
    showAd();
  }
};

// ========================================================== 华为RunTime SDK end =================================================

// ========================================================== 美团小游戏SDK start =================================================

P8MeituanSDK.init = async function () {
  let result = new Promise(async (resolve, reject) => {

    const urlConfigRes = await P8MeituanSDK.getUrlConfig();
    if (urlConfigRes.result !== 0) {
      console.error('[ 域名初始化失败 ] >', JSON.stringify(urlConfigRes));

      let response = {
        result: 1,
        data: {
          msg: "域名初始化失败",
          data: urlConfigRes
        }
      }
      resolve(response);
      return;
    }

    const payConfigRes = await P8MeituanSDK.getPayConfig();
    if (payConfigRes.result !== 0) {
      console.error('[ 支付配置参数获取失败 ] >', JSON.stringify(payConfigRes));

      let response = {
        result: 1,
        data: {
          msg: "支付配置参数获取失败",
          data: payConfigRes
        }
      }
      resolve(response);
      return;
    }

    let response = {
      result: 0,
      data: {
        msg: "初始化成功"
      }
    }
    resolve(response);
  });

  return result;
}

P8MeituanSDK.Login = async function (a) {
  let result = new Promise(async (resolve, reject) => {
    try {
      // 检查缓存的登录结果
      if (loginResult) {
        loginResult.msg = '此处表示sdk内部已经获取过这个值,再次调用会返回已经获取过的值';
        resolve(loginResult); // 登录过重复调用返回缓存值
        console.log('loginResult', loginResult);
        return;
      }

      // 检查本地缓存的登录信息
      const cachedLogin = await getLoginCache();
      if (cachedLogin) {
        // 恢复 sdkData 中的关键信息
        if (cachedLogin.data) {
          const cacheData = cachedLogin.data;
          if (cacheData.openid) {
            P8_QuickGame_Data.openid = cacheData.openid;
            P8_QuickGame_Data.device = cacheData.openid;
            P8_QuickGame_Data.uid = cacheData.uid;
            P8_QuickGame_Data.account = cacheData.account;
            P8_QuickGame_Data.password = cacheData.password;
            P8_QuickGame_Data.sessionid = cacheData.sessionid;
            P8_QuickGame_Data.sessiontime = cacheData.sessiontime;
            P8_QuickGame_Data.session_key = cacheData.session_key;
            P8_QuickGame_Data.istemp = cacheData.istemp;
          }
        }

        // 设置内存缓存
        loginResult = cachedLogin;

        // 添加缓存标识
        cachedLogin.msg = '从本地缓存获取登录信息';
        cachedLogin.fromCache = true;

        print('使用缓存的登录信息:', cachedLogin);

        resolve(cachedLogin);

        return;
      }

      mt.login({
        success(res) {
          // 美团登录
          let time = parseInt(new Date().getTime() / 1e3);
          // let url = `https://tcenter.play800.cn/mGame/login/${P8_QuickGame_Data.channelid}`
          let url = `${P8_QuickGame_Data.platform_url}/mGame/login/${P8_QuickGame_Data.channelid}`
          let login_data = {
            site: P8_QuickGame_Data.site,
            appid: P8_QuickGame_Data.appid,
            js_code: res.code,
            aid: P8_QuickGame_Data.aid,
            code: queryData.code,
            channel_parame: start_param,
            time: time,
          }
          ChangeUndefined(login_data);
          let n = newSignGetType_log(login_data);
          console.log('[ n ] >', n)
          let d = hex_md5(n);
          login_data.sign = d;
          console.log('[ play800登录请求参数 ] >', JSON.stringify(login_data));
          httpRequest({
            url: url,
            method: 'POST',
            data: login_data,
          }).then(response => {
            console.log(' [ play800登录返回 ] > ', JSON.stringify(response));
            if (response.data.result == 0) {
              let e = response.data.data;
              P8_QuickGame_Data.uid = e.uid + '';
              P8_QuickGame_Data.account = e.account;
              P8_QuickGame_Data.password = e.password;
              P8_QuickGame_Data.sessionid = e.sessionid;
              P8_QuickGame_Data.istemp = e.istemp;
              P8_QuickGame_Data.sessiontime = e.sessiontime;
              P8_QuickGame_Data.session_key = e.session_key;
              let md5Key = cry_md5(P8_QuickGame_Data.appid)
              let plainText = decrypt(wx.CryptoJS.mode.ECB, e.d, md5Key);
              P8_QuickGame_Data.openid = plainText;
              P8_QuickGame_Data.device = plainText;
              print("登录成功回调 进行赋值", JSON.stringify(P8_QuickGame_Data));
              let cp = {
                result: 0,
                data: {
                  openid: P8_QuickGame_Data.openid,
                  uid: P8_QuickGame_Data.uid,
                  account: P8_QuickGame_Data.account,
                  password: P8_QuickGame_Data.password,
                  sessionid: e.sessionid,
                  sessiontime: e.sessiontime,
                  session_key: e.session_key,
                }
              }
              Object.assign(cp.data, e);
              loginResult = cp;
              saveLoginCache(cp);
              resolve(loginResult);
            } else {
              print("play800登录异常:" + JSON.stringify(response));
              resolve(response);
            }
          }).catch(error => {
            console.error('登录失败', JSON.stringify(error));
            reject(error);
          });
        },
        fail: function (err) {
          console.log('[ 美团小游戏获取antuCode失败 ] >', JSON.stringify(err));
          resolve(err);
        },
      });

    } catch (error) {
      reject(error);
    }
  });

  return result;
}

P8MeituanSDK.getPayConfig = async function () {
  console.log('[ getPayConfig ] >', )
  let p = new Promise((resolve, reject) => {
    let url = `${P8_QuickGame_Data.platform_url}/mGame/payConf/${P8_QuickGame_Data.channelid}`
    // let url = `https://tcenter.play800.cn/mGame/payConf/${P8_QuickGame_Data.channelid}`
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      time: time,
    }
    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付配置参数请求 ] >', JSON.stringify(pay_data));
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(success => {
      let res = success.data;
      console.log(' [ play800支付配置参数返回 ] > ', JSON.stringify(res));
      if (res.result == 0 && res.data.is_set == true) {
        let response = {
          result: 0,
          data: {
            msg: "支付配置参数获取成功"
          }
        }
        resolve(response);
      } else if (res.result == 0 && res.data.is_set == false) {
        console.log("支付配置参数未设置");
        let response = {
          result: 1,
          data: {
            msg: "运营未在后台配置支付方式"
          }
        }
        resolve(response);
      } else if (res.result == 1) {
        console.log("支付配置参数接口请求失败");
        let response = {
          result: 1,
          data: {
            msg: "支付配置参数接口请求失败"
          }
        }
        resolve(response);
      }
    }).catch(error => {
      console.error('支付配置参数接口请求失败', JSON.stringify(error));
      reject(error);
    });
  });
  return p;
}

P8MeituanSDK.pay = function (a) {
  P8Log('进入美团小游戏支付', advancedObjectToString(a))

  let promise = new Promise((resolve, reject) => {
    let time = parseInt(new Date().getTime() / 1e3);
    let pay_data = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      cp_order_id: a.cpOrderId,
      roleid: a.roleId,
      rolename: a.userName,
      level: a.grade,
      serverid: a.serverId,
      productid: a.productId,
      product_name: a.productName,
      money: a.price / 100,
      ext: a.extraInfo,
      time: time,
      ip: a.ip,
      udid: P8_QuickGame_Data.openid,
      test: a.test ? a.test : '0',
    }

    if (pay_data.ext && typeof pay_data.ext == 'object') {
      pay_data.ext = JSON.stringify(pay_data.ext);
    }

    ChangeUndefined(pay_data);
    let n = newSignGetType_log(pay_data);
    let d = hex_md5(n);
    pay_data.sign = d;
    console.log('[ play800支付请求参数 ] >', JSON.stringify(pay_data));
    let url = `${P8_QuickGame_Data.rg_url}/mini/order/${P8_QuickGame_Data.channelid}`
    httpRequest({
      url: url,
      method: 'GET',
      data: pay_data,
    }).then(res => {
      console.log(' [ play800支付返回 ] > ', JSON.stringify(res));
      let response = res.data || res;
      if (response.result == 0) {
        let data = response.data;
        let p8order_id = data.order_id;
        console.log('[ 美团小游戏下单成功 ] >', p8order_id)

        const orderInfo = {
          mgcId: P8_QuickGame_Data.openid,
          appId: P8_QuickGame_Data.appid,
          accessToken: P8_QuickGame_Data.session_key,
          productId: a.productId,
          bizOrderNo: p8order_id,
          productName: a.productName,
          productDesc: a.productDesc || a.productName,
          productUrl: a.productUrl,
          needRefresh: a.needRefresh,
        }
        console.log('[ 美团小游戏支付请求参数 ] >', JSON.stringify(orderInfo));

        wx.requestMidasPayment({
          mgcId: P8_QuickGame_Data.openid,
          appId: P8_QuickGame_Data.appid,
          accessToken: P8_QuickGame_Data.session_key,
          productId: a.productId,
          bizOrderNo: p8order_id,
          productName: a.productName,
          productDesc: a.productDesc || a.productName,
          productUrl: a.productUrl,
          needRefresh: a.needRefresh,
          success: function (res) {
            console.log('[ 美团小游戏支付成功 ] >', JSON.stringify(res));
            let response = {
              result: 0,
              data: {
                msg: "支付成功"
              }
            }
            resolve(response);
          },
          fail: function (err) {
            console.error('美团小游戏支付异常', JSON.stringify(err));
            let response = {
              result: 1,
              data: {
                msg: "支付失败",
                error: err
              }
            }
            resolve(response);
          },
          complete: function (res) {

          }
        })
      } else {
        console.log('[ play800下单失败 ] >', JSON.stringify(response));
      }
    }).catch(error => {
      console.error('play800下单异常', JSON.stringify(error));
      reject(error);
    })
  })

  return promise;
}

P8MeituanSDK.getUrlConfig = async function () {
  console.log('[ getUrlConfig ] >', )
  let p = new Promise((resolve, reject) => {
    let url = `https://ksyun.oss-cn-hangzhou.aliyuncs.com/${P8_QuickGame_Data.aid}.txt`;
    httpRequest({
      url: url,
      method: 'GET',
    }).then(response => {
      let config = response.data || response;
      if (config.url_address) {
        let data_url = config.url_address.data_url;
        let rg_url = config.url_address.rg_url;
        let platform_url = config.url_address.platform_url;
        let dsj_url = config.url_address.dsj_url;
        console.log("data_url=" + data_url + ", rg_url=" + rg_url + ", platform_url=" + platform_url + ", dsj_url=" + dsj_url);

        Object.assign(P8_QuickGame_Data, {
          data_url: data_url, // 上报url
          rg_url: rg_url, //// 支付url
          platform_url: platform_url,
          dsj_url: dsj_url,
        });

        var response = {
          result: 0,
          data: {
            msg: "域名初始化成功"
          }
        };

      } else {
        var response = {
          result: 1,
          data: {
            msg: "域名获取失败，请联系运营配置"
          }
        };
      }
      resolve(response);
    }).catch(error => {
      console.error('域名获取失败', JSON.stringify(error));
      reject(error);
    });
  })

  return p;
}

P8MeituanSDK.adInitalization = function (e) {
  console.log("美团小游戏 广告初始化");
  // 激励视频初始化
  if (e && (typeof e === "string" || typeof e === "object")) {
    const adUnitId = typeof e === "string" ? e : e.adUnitId;
    if (adUnitId) {
      console.log("激励视频初始化", typeof e === "string" ? adUnitId : JSON.stringify(e));

      // 保存广告ID
      P8_QuickGame_Data.adList.ad_unit_id_reward = adUnitId;
      if (typeof e === "object" && e.adSlot) {
        P8_QuickGame_Data.adList.ad_slot_reward = e.adSlot;
      }

      // 销毁广告实例
      destroyVideoAd();

      // 创建广告实例
      qg_videoAD = mt.createCustomAd({
        posId: adUnitId,
      });

      // 重置状态
      isVideoShowInProgress = false;

      // 统一的事件处理
      qg_videoAD.onLoad(() => {
        print("激励视频 广告加载事件成功 initalization");
        adLoadState.isLoading = false;
        adLoadState.isReady = true;

        let result = {
          result: 0,
          data: {
            msg: "激励视频 广告加载事件成功"
          }
        }

        if (e.success) e.success(result);
      });

      qg_videoAD.onError((err) => {
        print("激励视频 广告加载异常 initalization", err);
        adLoadState.isLoading = false;
        adLoadState.isReady = false;
        isVideoShowInProgress = false;

        let result = {
          result: 1,
          data: {
            msg: "激励视频 广告加载异常",
            error: err
          }
        }

        if (e.fail) e.fail(result);
      });
    }
  }
};

P8MeituanSDK.videoADShow = function (t, a, c, onShow, adp) {
  // 检查是否在冷却时间内
  const now = Date.now();

  if (now - lastVideoShowTime < VIDEO_SHOW_COOLDOWN) {
    print("广告展示太频繁，请稍后再试");
    return;
  }

  // 检查是否有广告正在展示
  if (isVideoShowInProgress) {
    print("广告正在展示中");
    return;
  }

  P8_QuickGame_Data.ad_positon = adp || "";
  if (!qg_videoAD || !qg_videoAD.show) {
    print("激励视频不存在");
    return;
  }


  // 在新视频开始时初始化计时值
  ad_show_time = new Date().getTime();

  // 设置广告正在展示状态
  isVideoShowInProgress = true;

  // 重置状态锁
  isVideoLogReported = false;

  // 更新计时值
  lastVideoShowTime = now;

  // 优化的展示逻辑
  const showAd = () => {
    qg_videoAD.show().then(() => {
      if (onShow) onShow(); // 广告成功展示时触发回调

    }).catch((e) => {
      print("激励视频 广告加载异常 showAd", e);
      isVideoShowInProgress = false; // 重置状态
      if (c) c(e); // 广告加载异常 回调错误信息
    });
  };

  qg_videoAD.onError((err) => {
    print("激励视频 广告加载异常 showAd", err);
    isVideoShowInProgress = false; // 重置状态
    if (c) c(err); // 广告加载异常 回调错误信息
  });

  // 移除所有已存在的事件监听器
  qg_videoAD.offClose();

  // 准备上报数据
  let arg = {
    type: "RewardedVideoAd",
    status: "0",
    geType: "reward",
  }

  P8_QuickGame_Data.ad_unit_id = P8_QuickGame_Data.adList.ad_unit_id_reward;
  P8_QuickGame_Data.ad_slot = P8_QuickGame_Data.adList.ad_slot_reward;

  // 上报视频status = 0 状态
  if (!isVideoLogReported) {
    debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(arg)
    isVideoLogReported = true;
  }

  const i = (e) => {
    print("激励视频关闭", e)
    qg_videoAD.offClose(i);
    isVideoShowInProgress = false; // 重置状态

    if ((e && e.isRewarded)) {
      print("正常播放结束，可以下发游戏奖励");
      let succ = {
        type: "RewardedVideoAd",
        status: "1",
        geType: "reward",
      }

      // 重置状态锁并上报视频status = 1 完成状态
      isVideoLogReported = false;
      debounce(P8QuickGameSDK.wxVideoAutoLog, 1000)(succ)

      if (t) t()
    } else {
      print("播放中途退出，不下发游戏奖励");
      if (a) a()
    }

    // 预加载下一个广告
    if (!adLoadState.isLoading) {
      adLoadState.isLoading = true;
      qg_videoAD.load().finally(() => {
        adLoadState.isLoading = false;
      });
    }

  };

  // 添加事件监听器
  qg_videoAD.onClose(i);

  // 检查是否需要重新加载
  if (!adLoadState.isReady && !adLoadState.isLoading) {
    adLoadState.isLoading = true;
    qg_videoAD.load().then(() => {
      showAd();
    }).catch((err) => {
      print("激励视频 加载失败", err);
      if (c) c(err);
    }).finally(() => {
      adLoadState.isLoading = false;
      isVideoShowInProgress = false;
    });
  } else {
    showAd();
  }
};

P8MeituanSDK.addShortcut = function (a) {
  mt.addShortcut({
    shortcutType: a.shortcutType,
    id: a.id,
    label: a.label,
    icon: a.icon,
    target: a.target,
    widgetProviderId: a.widgetProviderId,
    success: function () {
      console.log('添加快捷方式成功');
    },
    fail: function (err) {
      console.log('添加快捷方式失败', advancedObjectToString(err));
    }
  })
}


// ========================================================== 美团小游戏SDK end =================================================

// ========================================================== 公共行为上报方法start ===============================================

// SDK激活上报
P8QuickGameSDK.onActiveFunc = function (g) {
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      time: t,
      device: P8_QuickGame_Data.device,
      ip: P8_QuickGame_Data.ip,
      mac: P8_QuickGame_Data.mac,
      modeltype: P8_QuickGame_Data.modeltype,
      gameversion: P8_QuickGame_Data.gameversion,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
    };
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/activate`;
    console.log("  激活上报请求服务器参数: " + advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("激活上报Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      console.log(" 激活返回的数据 res： " + advancedObjectToString(e));
      let t = dateOrRes(e);
      if (t.result == 0) {
        console.log("激活数据上报成功 ");
        var i = {
          result: "0",
          msg: "激活数据上报成功"
        };
      } else {
        console.log("激活数据上报失败 ");
        var i = {
          result: "1",
          data: {
            errorcode: t.data.errorcode,
            msg: t.data.msg
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(g, "activate");
    });
  });
  return e;
};

// 登录上报
P8QuickGameSDK.pushLoginData = function (s) {
  console.log(" =============== 开始登录上报: 传入的数据是 " + advancedObjectToString(s));
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      sid: s.sid || P8_QuickGame_Data.sid || '1',
      roleid: s.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
      rolename: s.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
      level: s.level || P8_QuickGame_Data.level || '1',
      vip: s.vip || P8_QuickGame_Data.vip || '1',
      ip: s.ip || P8_QuickGame_Data.ip,
      onlinetime: s.onlinetime,
      device: P8_QuickGame_Data.device,
      modeltype: P8_QuickGame_Data.modeltype,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
      oaid: s.oaid,
      site: P8_QuickGame_Data.site,
      time: t,
      version: P8QuickGameSDK_VERSION,
      game_type: 'mini'
    };
    P8_QuickGame_Data.sid = s.sid;
    P8_QuickGame_Data.roleid = s.roleid;
    P8_QuickGame_Data.rolename = s.rolename;
    P8_QuickGame_Data.level = s.level;
    P8_QuickGame_Data.vip = s.vip;
    setheartbeat(i);
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/login`;
    console.log(" 登录上报请求服务器参数: ", advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("登录上报请求Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      let t = dateOrRes(e);
      console.log("登录返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result) {
        console.log("登录数据上报失败 ");
        var i = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      } else {
        console.log("登录数据上报成功 ");
        var i = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "登录数据上报成功"
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(s, "login");
    });
  });
  return e;
};

// 创角上报
P8QuickGameSDK.signLog = function (o) {
  console.log(" =============== 开始创角上报: 传入的数据是 " + advancedObjectToString(o));
  let e = new Promise((r, e) => {
    ChangeUndefined(o);
    let s = o;
    if (!o) {
      s = {
        sid: "sid",
        uid: P8_QuickGame_Data.uid,
        roleid: "roleid",
        rolename: "rolename",
        device: "device",
        modeltype: "modeltype",
        mac: "mac",
        level: "level",
        gameversion: "gameversion",
        ip: "ip",
        device_model: "device_model",
        device_resolution: "device_resolution",
        device_version: "device_version",
        device_net: "device_net"
      };
    } else {
      s = {
        sid: o.sid || P8_QuickGame_Data.sid || '1',
        roleid: o.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
        rolename: o.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
        level: o.level || P8_QuickGame_Data.level || '1',
        gameversion: P8_QuickGame_Data.gameversion,
        uid: P8_QuickGame_Data.uid,
        device: P8_QuickGame_Data.device,
        modeltype: P8_QuickGame_Data.modeltype,
        mac: P8_QuickGame_Data.mac,
        ip: P8_QuickGame_Data.ip,
        device_model: P8_QuickGame_Data.device_model,
        device_resolution: P8_QuickGame_Data.device_resolution,
        device_version: P8_QuickGame_Data.device_version,
        device_net: P8_QuickGame_Data.device_net,
      }
    }
    s.game_type = 'mini';
    let t = parseInt(new Date().getTime() / 1e3);
    s.site = P8_QuickGame_Data.site;
    s.time = t;
    s.aid = P8_QuickGame_Data.aid;
    ChangeUndefined(s);
    let i = newSignGetType_log(s);
    var n = hex_md5(i);
    s.sign = n;
    let d = `${P8_QuickGame_Data.data_url}/log/role`;
    console.log("  创角上报请求服务器参数: " + advancedObjectToString(s));
    let a = d + "?" + keyValueConnect(s);
    console.log("创角上报请求Url: ", a);
    qgRequest(d, "GET", s, (e) => {
      let t = dateOrRes(e);
      console.log("创角返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result == 0) {
        console.log(P8_QuickGame_Data.uid ? "创角数据上报成功 " : "创角数据上报成功,但是获取uid失败了,上报了没有uid的数据");
        var i = {
          result: P8_QuickGame_Data.uid ? 0 : 1,
          data: {
            errorcode: P8_QuickGame_Data.uid ? 0 : 200,
            msg: P8_QuickGame_Data.uid ? "创角数据上报成功" : "创角上报了,获取uid失败了,上报了没有uid的数据"
          }
        };
      } else {
        console.log("创角数据上报失败");
        var i = {
          result: 1,
          data: {
            errorcode: 200,
            msg: "创角数据上报失败"
          }
        };
      }
      r(i);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(s, "role_create");
    });
  });
  return e;
};

// 角色升级上报
P8QuickGameSDK.upGradeRecord = function (s) {
  console.log(" =============== 开始升级上报: 传入的数据是 " + advancedObjectToString(s));
  let e = new Promise((r, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let i = {
      aid: P8_QuickGame_Data.aid,
      uid: P8_QuickGame_Data.uid,
      device: P8_QuickGame_Data.device,
      modeltype: P8_QuickGame_Data.modeltype,
      sid: s.sid || P8_QuickGame_Data.sid || '1',
      roleid: s.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || '1',
      rolename: s.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || '1',
      level: s.level || P8_QuickGame_Data.level || '1',
      vip: s.vip || P8_QuickGame_Data.vip || '1',
      ip: P8_QuickGame_Data.ip,
      onlinetime: s.onlinetime,
      device_model: P8_QuickGame_Data.device_model,
      device_resolution: P8_QuickGame_Data.device_resolution,
      device_version: P8_QuickGame_Data.device_version,
      device_net: P8_QuickGame_Data.device_net,
      oaid: s.oaid,
      site: P8_QuickGame_Data.site,
      time: t,
      version: P8QuickGameSDK_VERSION
    };
    P8_QuickGame_Data.sid = s.sid;
    P8_QuickGame_Data.roleid = s.roleid;
    P8_QuickGame_Data.rolename = s.rolename;
    P8_QuickGame_Data.level = s.level;
    P8_QuickGame_Data.vip = s.vip;
    ChangeUndefined(i);
    let n = newSignGetType_log(i);
    var d = hex_md5(n);
    i.sign = d;
    let a = `${P8_QuickGame_Data.data_url}/log/level`;
    console.log("升级上报请求服务器的参数: " + advancedObjectToString(i));
    let o = a + "?" + keyValueConnect(i);
    console.log("升级上报请求Url: ", o);
    qgRequest(a, "GET", i, (e) => {
      let t = dateOrRes(e);
      console.log("升级数据上报返回的数据 是什么 " + advancedObjectToString(t));
      if (t.result == 0) {
        var res = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "升级数据上报成功"
          }
        };
      } else {
        var res = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      }
      r(res);
      debounce(P8QuickGameSDK.dsjAotLog, 1000)(i, "role_upgrade");
    });
  });
  return e;
};

// 广告自动上报
P8QuickGameSDK.wxVideoAutoLog = function (arg) {
  console.log(" =============== 开始微信广告自动上报逻辑: 数据是 " + JSON.stringify(arg));
  let e = new Promise((i, e) => {
    let t = parseInt(new Date().getTime() / 1e3);
    let data = {
      site: P8_QuickGame_Data.site,
      aid: P8_QuickGame_Data.aid,
      time: t,
      device_type: P8_QuickGame_Data.platform,

      sid: arg.sid || P8_QuickGame_Data.sid || "1",
      uid: P8_QuickGame_Data.uid,
      device: P8_QuickGame_Data.device,
      roleid: arg.roleid || P8_QuickGame_Data.roleid || P8_QuickGame_Data.uid || "1",
      rolename: arg.rolename || P8_QuickGame_Data.rolename || P8_QuickGame_Data.uid || "1",
      level: arg.level || P8_QuickGame_Data.level || "1",
      game_type: 'mini',
      ad_slot: arg.ad_slot || P8_QuickGame_Data.ad_slot || "激励视频", // 广告位创建的名称 在微信后台申请的广告位的名称
      ad_unit_id: arg.ad_unit_id || P8_QuickGame_Data.ad_unit_id || "1", //广告位id
      type: arg.type, // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
      status: arg.status, // 点击传入 0 观看成功传入 1 banner广告点击就算成功
      ad_positon: arg.ad_positon || P8_QuickGame_Data.ad_positon || "", // 广告展示位置
    };
    ChangeUndefined(data);
    let n = newSignGetType_log(data);
    var r = hex_md5(n);
    data.sign = r;
    console.log('[ 微信广告自动上报请求服务器参数 ] >', advancedObjectToString(data))
    let url = `${P8_QuickGame_Data.data_url}/log/wxRewardedAd`;
    let rUrl = url + "?" + keyValueConnect(data);
    console.log("微信广告自动上报请求Url: ", rUrl)
    qgRequest(url, "GET", data, (e) => {
      console.log("微信广告自动上报返回的数据 是什么 " + advancedObjectToString(e));
      let t = dateOrRes(e);
      if (t.result == 0) {
        var res = {
          result: "0",
          data: {
            errorcode: 200,
            msg: "微信广告上报成功"
          }
        };
      } else {
        var res = {
          result: "1",
          data: {
            errorcode: t.errorcode,
            msg: t.msg
          }
        };
      }
      i(res);
      P8QuickGameSDK.wxVideoLogNew(data);
    });
  });
  return e;
};

P8QuickGameSDK.dsjAotLog = function (params, event_name) {

  let inData = {
    ...params,
    ...publicParameters()
  };

  let t = parseInt(new Date().getTime() / 1e3);

  let logData = {
    event_name: event_name,
    event_time: t,
    data: inData,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  }

  let url = `${P8_QuickGame_Data.dsj_url}/sdk/upload`;
  console.log(` 大数据上报${event_name}请求服务器参数: ` + advancedObjectToString(logData));
  qgRequest(url, "POST", logData, (e) => {
    let response = dateOrRes(e);
    console.log(` 大数据上报${event_name}返回的数据 res： ` + advancedObjectToString(response));
  });
}

P8QuickGameSDK.wxVideoLogNew = function (arg = "{}") {
  let t = parseInt(new Date().getTime() / 1e3);

  // 添加时间戳检查
  if (t === lastVideoReportTime) {
    console.log("防止重复调用：当前时间戳与上次相同");
    return;
  }
  lastVideoReportTime = t; // 更新最后上报时间

  let nowTime = new Date().getTime();
  let endTime = nowTime - ad_show_time;
  let data = {
    site: P8_QuickGame_Data.site,
    aid: P8_QuickGame_Data.aid,
    sid: arg.sid,
    uid: P8_QuickGame_Data.uid,
    device_type: P8_QuickGame_Data.platform,
    device: P8_QuickGame_Data.device,
    ip: P8_QuickGame_Data.ip,
    roleid: arg.roleid,
    rolename: arg.rolename,
    level: arg.level,
    game_type: 'mini',
    ad_slot: arg.ad_slot || P8_QuickGame_Data.ad_slot || "激励视频", // 广告位创建的名称 在微信后台申请的广告位的名称
    ad_unit_id: arg.ad_unit_id || P8_QuickGame_Data.ad_unit_id || "1", //广告位id
    ad_status: arg.status, // 点击传入 0 观看成功传入 1 banner广告点击就算成功
    ad_type: arg.type, // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
    ad_positon: arg.ad_positon || P8_QuickGame_Data.ad_positon || "", // 广告展示位置
    username: arg.username || P8_QuickGame_Data.account,
    device_model: P8_QuickGame_Data.device_model,
    ad_show_time: arg.status == 0 ? 0 : Math.floor((endTime) / 1000),
    vip: arg.vip,
    ad_bid: "1",
    media_params: start_param,
    device_code: P8_QuickGame_Data.device,
    game_id: P8_QuickGame_Data.game_id,
    is_model: 1,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  };
  ChangeUndefined(data);
  let wxVideoLog = {
    event_name: "ad_show",
    event_time: t,
    data,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`
  };
  let d = `${P8_QuickGame_Data.dsj_url}/sdk/upload`;
  console.log(" 大数据广告上报请求服务器参数: " + advancedObjectToString(wxVideoLog));
  qgRequest(d, "POST", wxVideoLog, (e) => {
    let data = dateOrRes(e);
    console.log(" 大数据广告返回的数据 res： " + advancedObjectToString(data));
  });
}

function publicParameters() {
  let params = {
    site: P8_QuickGame_Data.site,
    aid: P8_QuickGame_Data.aid,
    uid: P8_QuickGame_Data.uid,
    device: P8_QuickGame_Data.device,
    device_type: P8_QuickGame_Data.modeltype,
    username: P8_QuickGame_Data.account,
    ip: P8_QuickGame_Data.ip,
    mac: P8_QuickGame_Data.mac,
    gameversion: P8_QuickGame_Data.gameversion,
    device_model: P8_QuickGame_Data.device_model,
    device_resolution: P8_QuickGame_Data.device_resolution,
    device_version: P8_QuickGame_Data.device_version,
    device_net: P8_QuickGame_Data.device_net,
    game_type: 'mini',
    media_params: start_param,
    device_code: P8_QuickGame_Data.device,
    game_id: P8_QuickGame_Data.game_id,
    is_model: 1,
    sdk_version: P8QuickGameSDK_VERSION,
    sdk_name: `P8-聚合快应用SDK-${P8_QuickGame_Data.platform}快游戏-p8sdk-quickGame-${P8QuickGameSDK_VERSION}`,
    source_type: 3,
    source_from: ""
  }
  return params;
}

function setheartbeat(e) {
  console.log('[ 心跳时间上报开始 ] >', advancedObjectToString(e))
  let t = setInterval(() => {
    console.log("计时器");
    heartbeat(e);
  }, 3e5);
  console.log("time =", t);
}

function heartbeat(e) {
  let t = parseInt(new Date().getTime() / 1e3);
  console.log("***t", t);
  let i = {
    aid: e.aid,
    uid: e.uid,
    site: e.site,
    device: e.device,
    modeltype: e.modeltype,
    username: e.username,
    sid: e.sid,
    roleid: e.roleid,
    rolename: e.rolename,
    level: e.level,
    vip: e.vip,
    ip: e.ip,
    onlinetime: e.onlinetime,
    device_model: e.device_model,
    device_resolution: e.device_resolution,
    device_version: e.device_version,
    device_net: e.device_net,
    time: t,
    oaid: e.oaid
  };

  ChangeUndefined(i);
  let r = newSignGetType_log(i);
  var n = hex_md5(r);
  i.sign = n;
  console.log('[ 心跳时间上报请求服务器参数 ] >', advancedObjectToString(i))
  let d = `${P8_QuickGame_Data.data_url}/log/onlineTime`;
  let rUrl = d + "?" + keyValueConnect(i);
  console.log('[ 心跳时间上报请求Url ] >', rUrl)
  qgRequest(d, "GET", i, (e) => {
    var t = dateOrRes(e);
    console.log("心跳时间上报= ", advancedObjectToString(t));
  });
}

// ========================================================== 公共行为上报方法end =================================================

// ========================================================== unit方法start ===============================================

P8QuickGameSDK.print = function (...args) {
  // 基本日志前缀
  const prefix = '[聚合SDK] 日志';

  // 检查是否包含复杂对象
  const hasComplexObjects = args.some(arg =>
    arg !== null && typeof arg === 'object'
  );

  if (hasComplexObjects) {
    // 打印基本日志
    console.log(prefix);
    // 逐个打印参数，对对象使用更详细的输出
    args.forEach((arg, index) => {
      if (arg !== null && typeof arg === 'object') {
        console.log(`参数${index + 1}:`, JSON.stringify(arg, null, 2));
      } else {
        console.log(`参数${index + 1}:`, arg);
      }
    });
  } else {
    // 简单值直接使用标准打印
    console.log(prefix, ...args);
  }
}

function qgRequest(e, a = "post", t = {}, n = null, f = null, i = true) {
  const requestParams = {
    url: e,
    method: a,
    data: t,
    dataType: "json",
    success: (success) => {
      if (n) {
        n(success);
      }
    },
    fail: (error) => {
      if (f) {
        f(error);
      }
    },
  }
  if (typeof qg !== 'undefined' && qg.request) {
    qg.request(requestParams);
  } else if (typeof mt !== 'undefined' && mt.request) {
    mt.request(requestParams);
  } else if (typeof wx !== 'undefined' && wx.request) {
    wx.request(requestParams);
  } else {
    let r = new XMLHttpRequest;
    let url = e + "?" + keyValueConnect(t);
    r.open(a, url);
    if (i) {
      r.setRequestHeader("Content-type", "application/json")
    } else {
      console.log("术良 url" + url);
      r.setRequestHeader("Content-type", "application/x-www-form-urlencoded")
    }
    r.send(JSON.stringify(t));
    r.onreadystatechange = function () {
      if (r.status == 200 && r.readyState == 4) {
        let e = JSON.parse(r.responseText);
        console.log("请求返回的数据是什么:" + JSON.stringify(e));
        if (n) {
          n(e)
        }
      }
    }
  }
}

function P8Log(e) {
  console.log(" ======== [聚合SDK] 日志 ========>" + e)
}

function print() {
  let t = "";
  for (let e = 0; e < 20; e++) {
    if (!arguments[e]) {
      console.log(t);
      return;
    }
    t += " " + JSON.stringify(arguments[e]);
  }
}

function advancedObjectToString(obj, depth = 0, maxDepth = 10) {
  // 处理基础类型和特殊情况
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';
  if (typeof obj === 'function') return '[Function]';
  if (typeof obj !== 'object') return String(obj);
  if (depth > maxDepth) return '[Object]'; // 防止过深递归

  // 处理数组
  if (Array.isArray(obj)) {
    const items = obj.map(item => advancedObjectToString(item, depth + 1, maxDepth));
    return `[${items.join(', ')}]`;
  }

  // 处理普通对象
  const indent = '  '.repeat(depth);
  const pairs = [];

  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      try {
        const value = obj[key];
        pairs.push(`${key}: ${advancedObjectToString(value, depth + 1, maxDepth)}`);
      } catch (e) {
        pairs.push(`${key}: [Error: ${e.message}]`);
      }
    }
  }

  return `{\n${indent}  ${pairs.join(',\n' + indent + '  ')}\n${indent}}`;
}

function getNonce() {
  let s = "";
  for (let i = 0; i < 32; i++) {
    let tmp = Math.floor(Math.random() * 10);
    s = s + tmp.toString();
  }
  return s;
}

function dateFormat() {
  let date = new Date();
  let ret;
  let fmt = "yyyyMMddHHmmss";
  const opt = {
    "y+": date.getFullYear().toString(), // 年
    "M+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "m+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString() // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  let mm = date.getMinutes();
  let HH = date.getHours();
  if (mm + 10 > 60) {
    mm = mm - 50;
    HH = HH + 1;
  } else {
    mm = mm + 10;
  }
  opt["m+"] = mm.toString()
  opt["H+"] = HH.toString()
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
    };
  };
  return fmt;
}

function ChangeUndefined(t) {
  for (let e in t) {
    if (t.hasOwnProperty(e)) {
      if (typeof t[e] == "undefined") {
        t[e] = "";
      }
    }
  }
}

function newSignGetType_log(t) {
  var i = [];
  for (var e in t) {
    i.push(e);
  }
  i = i.sort();
  let r = P8_QuickGame_Data.site;
  for (let e = 0; e < i.length; e++) {
    const n = t[i[e]];
    if (e != 0) {
      r += "&" + i[e] + "=" + n;
    } else {
      r += i[e] + "=" + n;
    }
  }
  r += P8_QuickGame_Data.key;
  return r;
}

function dateOrRes(e) {
  return e.data ? e.data : e;
}

function keyValueConnect(e) {
  let a = "";
  for (const t in e) {
    if (e.hasOwnProperty.call(e, t)) {
      const n = e[t];
      a += t + "=" + n + "&"
    }
  }
  a = a.substring(0, a.length - 1);
  return a
}

function getTime() {
  var e = new Date;
  let a = e.getFullYear();
  let t = e.getMonth() + 1;
  let n = e.getDate();
  let i = e.getHours();
  let r = e.getMinutes();
  let o = e.getSeconds();
  return a + "/" + t + "/" + n + " " + i + ":" + r + ":" + o
}

function debounce(fn, delay = 1000) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  }
}

function destroyVideoAd() {
  if (qg_videoAD) {
    // 移除所有事件监听
    qg_videoAD.offLoad();
    qg_videoAD.offError();
    qg_videoAD.offClose();

    // 销毁实例
    if (typeof qg_videoAD.destroy === 'function') {
      qg_videoAD.destroy();
    }

    qg_videoAD = null;
  }
  // 重置状态
  adLoadState.isLoading = false;
  adLoadState.isReady = false;
}

/**
 * 通用HTTP请求封装函数
 * @param {Object} options 请求选项
 * @param {string} options.url 请求URL
 * @param {string} [options.method='GET'] 请求方法(GET, POST, PUT, DELETE等)
 * @param {Object} [options.data={}] 请求数据
 * @param {Object} [options.headers={}] 请求头
 * @param {boolean} [options.useJSON=true] 是否使用JSON格式
 * @param {number} [options.timeout=30000] 超时时间(毫秒)
 * @param {boolean} [options.usePlatformAPI=true] 是否优先使用平台API(如qg.request)
 * @returns {Promise<any>} 返回Promise对象
 */
function httpRequest(options) {
  // 参数默认值和校验
  const url = options.url;
  if (!url) {
    return Promise.reject(new Error('URL不能为空'));
  }

  const method = (options.method || 'GET').toUpperCase();
  const data = options.data || {};
  const headers = options.headers || {};
  const useJSON = options.useJSON !== false;
  const timeout = options.timeout || 30000;
  const usePlatformAPI = options.usePlatformAPI !== false;

  // 返回Promise
  return new Promise((resolve, reject) => {
    // 构建请求参数
    const requestParams = {
      url: url,
      method: method,
      data: data,
      dataType: 'json',
      success: (res) => {
        resolve(res);
      },
      fail: (error) => {
        reject(error);
      }
    };

    // 优先使用平台API
    if (usePlatformAPI && typeof qg !== 'undefined' && qg.request) {
      console.log('[ 使用qg.request ] >', advancedObjectToString(requestParams))
      // 如果有指定headers，添加到请求中
      if (headers && Object.keys(headers).length > 0) {
        requestParams.header = headers;
      }

      // 设置超时
      if (timeout) {
        requestParams.timeout = timeout;
      }

      // 发起请求
      qg.request(requestParams);
    } else if (usePlatformAPI && typeof wx !== 'undefined' && wx.request) {
      console.log('[ 使用wx.request ] >', advancedObjectToString(requestParams))
      wx.request(requestParams);
    } else if (usePlatformAPI && typeof mt !== 'undefined' && mt.request) {
      console.log('[ 使用mt.request ] >', advancedObjectToString(requestParams))
      mt.request(requestParams);
    } else {
      // 回退到XMLHttpRequest
      console.log('[ 回退到XMLHttpRequest ] >', advancedObjectToString(requestParams))
      try {
        const xhr = new XMLHttpRequest();
        let urlWithParams = url;

        // 处理GET请求参数
        if (method === 'GET' && data && Object.keys(data).length > 0) {
          const params = [];
          for (const key in data) {
            if (data.hasOwnProperty(key)) {
              params.push(`${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`);
            }
          }
          urlWithParams = `${url}${url.includes('?') ? '&' : '?'}${params.join('&')}`;
        }

        // 打开连接
        xhr.open(method, urlWithParams, true);

        // 设置超时
        if (timeout) {
          xhr.timeout = timeout;
        }

        // 设置请求头
        if (useJSON && method !== 'GET') {
          xhr.setRequestHeader('Content-Type', 'application/json');
        } else if (method !== 'GET') {
          xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        }

        // 添加自定义请求头
        if (headers) {
          for (const key in headers) {
            if (headers.hasOwnProperty(key)) {
              xhr.setRequestHeader(key, headers[key]);
            }
          }
        }

        // 注册事件回调
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText);
                resolve(response);
              } catch (e) {
                // 非JSON响应
                resolve(xhr.responseText);
              }
            } else {
              reject({
                status: xhr.status,
                statusText: xhr.statusText,
                response: xhr.responseText
              });
            }
          }
        };

        xhr.onerror = function () {
          reject({
            status: xhr.status,
            statusText: '请求失败',
            error: new Error('Network Error')
          });
        };

        xhr.ontimeout = function () {
          reject({
            status: 408,
            statusText: '请求超时',
            error: new Error('Request timeout')
          });
        };

        // 发送请求
        if (method === 'GET') {
          xhr.send();
        } else {
          xhr.send(useJSON ? JSON.stringify(data) : httpkeyValueConnect(data));
        }
      } catch (error) {
        reject({
          status: 0,
          statusText: '请求异常',
          error: error
        });
      }
    }
  });
}

/**
 * 将对象转换为key=value&key=value格式字符串
 * @param {Object} data 要转换的对象
 * @returns {string} 表单格式字符串
 */
function httpkeyValueConnect(data) {
  if (!data || typeof data !== 'object') {
    return '';
  }

  let result = '';
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const value = data[key];
      result += `${key}=${value}&`;
    }
  }

  return result.length > 0 ? result.substring(0, result.length - 1) : '';
}

// 辅助函数
function cry_md5(data) {
  return wx.CryptoJS.MD5(data).toString();
}

// 传入key之前要调用，不然结果不对
function parseKey(key) {
  return wx.CryptoJS.enc.Utf8.parse(key);
}

// 解密过程
function decrypt(mode, cipherText, key, iv = null) {
  const uKey = parseKey(key);
  const uIv = parseKey(iv);
  let bytes = wx.CryptoJS.AES.decrypt(cipherText, uKey, {
    iv: uIv,
    mode: mode,
    padding: wx.CryptoJS.pad.Pkcs7
  });
  // console.log('bytes', bytes.toString(wx.CryptoJS.enc.Base64));
  return bytes.toString(wx.CryptoJS.enc.Utf8);
}

// 等待CryptoJS加载
function waitForCryptoJS(callback) {
  if (wx.CryptoJS && wx.CryptoJS.mode && wx.CryptoJS.AES) {
    callback();
  } else {
    setTimeout(() => waitForCryptoJS(callback), 100);
  }
}

const LOGIN_CACHE_KEY = '@P8SDK_LOGIN_CACHE';
const LOGIN_CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 24小时 默认正式使用
// const LOGIN_CACHE_EXPIRE_TIME = 2 * 60 * 1000; // 2分钟 Debug测试使用


// 检查登录缓存是否有效
function isLoginCacheValid(cacheData) {
  print('检查登录缓存是否有效');
  return new Promise((resolve) => {

    // 检查缓存数据是否存在或格式错误
    if (!cacheData || !cacheData.timestamp || !cacheData.loginData) {
      print('登录缓存数据不存在或格式错误');
      resolve(false);
      return;
    }

    // 检查缓存是否过期
    const now = Date.now();
    const cacheTime = cacheData.timestamp;
    const isExpired = now - cacheTime > LOGIN_CACHE_EXPIRE_TIME;
    if (isExpired) {
      print('登录缓存已过期');
      resolve(false);
      return;
    }

    // 检查必要字段是否存在
    const loginData = cacheData.loginData;
    if (!loginData.data || !loginData.data.openid || !loginData.data.session_key || !loginData.data.uid) {
      print('登录缓存数据不完整');
      resolve(false);
      return;
    }

    // 使用 wx.checkSession 验证微信登录状态
    try {
      wx.checkSession({
        success() {
          //session_key 未过期，并且在本生命周期一直有效
          print('session_key 未过期，并且在本生命周期一直有效');
          resolve(true);
        },
        fail() {
          // session_key 已经失效，需要重新执行登录流程
          print('session_key 已经失效，需要重新执行登录流程');
          resolve(false);
        }
      })
    } catch (error) {
      console.error('验证微信登录状态失败:', error);
      resolve(false);
    }

  });
}

// 保存登录信息到缓存
function saveLoginCache(loginData) {
  try {
    const cacheData = {
      timestamp: Date.now(),
      loginData: loginData,
      version: '1.0' // 用于后续版本兼容
    };

    setItem(LOGIN_CACHE_KEY, cacheData);
    print('登录信息已缓存到本地');
    return true;
  } catch (error) {
    console.error('保存登录缓存失败:', error);
    return false;
  }
}

// 获取缓存的登录信息
function getLoginCache() {
  return new Promise(async (resolve) => {
    try {
      const cacheData = getItem(LOGIN_CACHE_KEY);
      const isValid = await isLoginCacheValid(cacheData);

      if (isValid) {
        print('从本地缓存获取登录信息');
        resolve(cacheData.loginData);
        return;
      }

      // 缓存无效时清除
      if (cacheData) {
        removeItem(LOGIN_CACHE_KEY);
        print('删除无效的登录缓存');
      }

      resolve(null);
    } catch (error) {
      console.error('获取登录缓存失败:', error);
      resolve(null);
    }

  });
}

/** 获取本地缓存文件 */
function getItem(key) {
  try {
    return wx.getStorageSync(key);
  } catch (error) {
    console.error('获取本地缓存失败:', error);
    return null;
  }
}

/** 设置本地缓存文件 */
function setItem(key, value) {
  try {
    wx.setStorageSync(key, value);
  } catch (error) {
    console.error('设置本地缓存失败:', error);
  }
}

/** 清除本地缓存文件 */
function clearItem(key) {
  try {
    wx.clearStorageSync(key)
  } catch (error) {
    console.error('清除本地缓存失败:', error);
  }
}

/** 删除本地缓存文件 */
function removeItem(key) {
  try {
    wx.removeStorageSync(key)
  } catch (error) {
    console.error('删除本地缓存失败:', error);
  }
}

// ========================================================== unit方法end =================================================

// ========================================================== md5方法start =================================================

var V = V || {};
V.Security = V.Security || {};
(function () {
  // for faster access
  var S = V.Security;
  /**
   * The highest integer value a number can go to without losing precision.
   */
  S.maxExactInt = Math.pow(2, 53);
  /**
   * Converts string from internal UTF-16 to UTF-8
   * and saves it using array of numbers (bytes), 0-255 per cell
   * @param {String} str
   * @return {Array}
   */
  S.toUtf8ByteArr = function (str) {
    var arr = [],
      code;
    for (var i = 0; i < str.length; i++) {
      code = str.charCodeAt(i);
      /*
                  Note that charCodeAt will always return a value that is less than 65,536.
                  This is because the higher code points are represented by a pair of (lower valued)
                  "surrogate" pseudo-characters which are used to comprise the real character.
                  Because of this, in order to examine or reproduce the full character for
                  individual characters of value 65,536 and above, for such characters,
                  it is necessary to retrieve not only charCodeAt(0), but also charCodeAt(1). 
                   */
      if (0xd800 <= code && code <= 0xdbff) {
        // UTF-16 high surrogate
        var hi = code,
          low = str.charCodeAt(i + 1);
        code = (hi - 0xd800) * 0x400 + (low - 0xdc00) + 0x10000;
        i++;
      }
      if (code <= 127) {
        arr[arr.length] = code;
      } else if (code <= 2047) {
        arr[arr.length] = (code >>> 6) + 0xc0;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else if (code <= 65535) {
        arr[arr.length] = (code >>> 12) + 0xe0;
        arr[arr.length] = ((code >>> 6) & 0x3f) | 0x80;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else if (code <= 1114111) {
        arr[arr.length] = (code >>> 18) + 0xf0;
        arr[arr.length] = ((code >>> 12) & 0x3f) | 0x80;
        arr[arr.length] = ((code >>> 6) & 0x3f) | 0x80;
        arr[arr.length] = (code & 0x3f) | 0x80;
      } else {
        throw "Unicode standart supports code points up-to U+10FFFF";
      }
    }
    return arr;
  };
  /**
   * Outputs 32 integer bits of a number in hex format.
   * Preserves leading zeros.
   * @param {Number} num
   */
  S.toHex32 = function (num) {
    // if negative
    if (num & 0x80000000) {
      // convert to positive number
      num = num & ~0x80000000;
      num += Math.pow(2, 31);
    }
    var str = num.toString(16);
    while (str.length < 8) {
      str = "0" + str;
    }
    return str;
  };
  /**
   * Changes the order of 4 bytes in integer representation of number.
   * From 1234 to 4321.
   * @param {Number} num Only 32 int bits are used.
   */
  S.reverseBytes = function (num) {
    var res = 0;
    res += (num >>> 24) & 0xff;
    res += ((num >>> 16) & 0xff) << 8;
    res += ((num >>> 8) & 0xff) << 16;
    res += (num & 0xff) << 24;
    return res;
  };
  S.leftRotate = function (x, c) {
    return (x << c) | (x >>> (32 - c));
  };
  /**
   * RSA Data Security, Inc. MD5 Message-Digest Algorithm
   * http://tools.ietf.org/html/rfc1321
   * http://en.wikipedia.org/wiki/MD5
   * @param {String} message
   */
  S.md5 = function (message) {
    // r specifies the per-round shift amounts
    var r = [
      7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 6, 10, 15, 21, 6,
      10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
    ];
    // Use binary integer part of the sines of integers (Radians) as constants:
    var k = [];
    for (var i = 0; i <= 63; i++) {
      k[i] = (Math.abs(Math.sin(i + 1)) * Math.pow(2, 32)) << 0;
    }
    var h0 = 0x67452301,
      h1 = 0xefcdab89,
      h2 = 0x98badcfe,
      h3 = 0x10325476,
      bytes,
      unpadded;
    //Pre-processing:
    bytes = S.toUtf8ByteArr(message);
    message = null;
    unpadded = bytes.length;
    //append "1" bit to message
    //append "0" bits until message length in bits ≡ 448 (mod 512)
    bytes.push(0x80);
    var zeroBytes = Math.abs(448 - ((bytes.length * 8) % 512)) / 8;
    while (zeroBytes--) {
      bytes.push(0);
    }
    //append bit length of unpadded message as 64-bit little-endian integer to message
    bytes.push((unpadded * 8) & 0xff, ((unpadded * 8) >> 8) & 0xff, ((unpadded * 8) >> 16) & 0xff, ((unpadded * 8) >> 24) & 0xff);
    var i = 4;
    while (i--) {
      bytes.push(0);
    }
    var leftRotate = S.leftRotate;
    //Process the message in successive 512-bit chunks:
    var i = 0,
      w = [];
    while (i < bytes.length) {
      //break chunk into sixteen 32-bit words w[i], 0 ≤ i ≤ 15
      for (var j = 0; j <= 15; j++) {
        w[j] = (bytes[i + 4 * j] << 0) + (bytes[i + 4 * j + 1] << 8) + (bytes[i + 4 * j + 2] << 16) + (bytes[i + 4 * j + 3] << 24);
      }
      //Initialize hash value for this chunk:
      var a = h0,
        b = h1,
        c = h2,
        d = h3,
        f,
        g;
      //Main loop:
      for (var j = 0; j <= 63; j++) {
        if (j <= 15) {
          f = (b & c) | (~b & d);
          g = j;
        } else if (j <= 31) {
          f = (d & b) | (~d & c);
          g = (5 * j + 1) % 16;
        } else if (j <= 47) {
          f = b ^ c ^ d;
          g = (3 * j + 5) % 16;
        } else {
          f = c ^ (b | ~d);
          g = (7 * j) % 16;
        }
        var temp = d;
        d = c;
        c = b;
        b = b + leftRotate(a + f + k[j] + w[g], r[j]);
        a = temp;
      }
      //Add this chunk's hash to result so far:
      h0 = (h0 + a) << 0;
      h1 = (h1 + b) << 0;
      h2 = (h2 + c) << 0;
      h3 = (h3 + d) << 0;
      i += 512 / 8;
    }
    // fix when starting with 0
    var res = out(h0) + out(h1) + out(h2) + out(h3);

    function out(h) {
      return S.toHex32(S.reverseBytes(h));
    }
    return res;
  };
})();

var hexcase = 0; /* hex output format. 0 - lowercase; 1 - uppercase        */
var b64pad = ""; /* base-64 pad character. "=" for strict RFC compliance   */

function hex_md5(s) {
  return rstr2hex(rstr_md5(str2rstr_utf8(s)));
}

function rstr_md5(s) {
  return binl2rstr(binl_md5(rstr2binl(s), s.length * 8));
}

function rstr2hex(input) {
  try {
    hexcase;
  } catch (e) {
    hexcase = 0;
  }
  var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
  var output = "";
  var x;
  for (var i = 0; i < input.length; i++) {
    x = input.charCodeAt(i);
    output += hex_tab.charAt((x >>> 4) & 0x0f) + hex_tab.charAt(x & 0x0f);
  }
  return output;
}

function str2rstr_utf8(input) {
  return unescape(encodeURI(input));
}

function rstr2binl(input) {
  var output = Array(input.length >> 2);
  for (var i = 0; i < output.length; i++) output[i] = 0;
  for (var i = 0; i < input.length * 8; i += 8) output[i >> 5] |= (input.charCodeAt(i / 8) & 0xff) << i % 32;
  return output;
}

function binl2rstr(input) {
  var output = "";
  for (var i = 0; i < input.length * 32; i += 8) output += String.fromCharCode((input[i >> 5] >>> i % 32) & 0xff);
  return output;
}

function binl_md5(x, len) {
  /* append padding */
  x[len >> 5] |= 0x80 << len % 32;
  x[(((len + 64) >>> 9) << 4) + 14] = len;
  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a = md5_ff(a, b, c, d, x[i + 0], 7, -680876936);
    d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);
    a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5_gg(b, c, d, a, x[i + 0], 20, -373897302);
    a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);
    a = md5_hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5_hh(d, a, b, c, x[i + 0], 11, -358537222);
    c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651);
    a = md5_ii(a, b, c, d, x[i + 0], 6, -198630844);
    d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551);
    a = safe_add(a, olda);
    b = safe_add(b, oldb);
    c = safe_add(c, oldc);
    d = safe_add(d, oldd);
  }
  return Array(a, b, c, d);
}

function md5_cmn(q, a, b, x, s, t) {
  return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
}

function md5_ff(a, b, c, d, x, s, t) {
  return md5_cmn((b & c) | (~b & d), a, b, x, s, t);
}

function md5_gg(a, b, c, d, x, s, t) {
  return md5_cmn((b & d) | (c & ~d), a, b, x, s, t);
}

function md5_hh(a, b, c, d, x, s, t) {
  return md5_cmn(b ^ c ^ d, a, b, x, s, t);
}

function md5_ii(a, b, c, d, x, s, t) {
  return md5_cmn(c ^ (b | ~d), a, b, x, s, t);
}

function safe_add(x, y) {
  var lsw = (x & 0xffff) + (y & 0xffff);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return (msw << 16) | (lsw & 0xffff);
}

function bit_rol(num, cnt) {
  return (num << cnt) | (num >>> (32 - cnt));
}

function md5cycle(x, k) {
  var a = x[0],
    b = x[1],
    c = x[2],
    d = x[3];
  a = ff(a, b, c, d, k[0], 7, -680876936);
  d = ff(d, a, b, c, k[1], 12, -389564586);
  c = ff(c, d, a, b, k[2], 17, 606105819);
  b = ff(b, c, d, a, k[3], 22, -1044525330);
  a = ff(a, b, c, d, k[4], 7, -176418897);
  d = ff(d, a, b, c, k[5], 12, 1200080426);
  c = ff(c, d, a, b, k[6], 17, -1473231341);
  b = ff(b, c, d, a, k[7], 22, -45705983);
  a = ff(a, b, c, d, k[8], 7, 1770035416);
  d = ff(d, a, b, c, k[9], 12, -1958414417);
  c = ff(c, d, a, b, k[10], 17, -42063);
  b = ff(b, c, d, a, k[11], 22, -1990404162);
  a = ff(a, b, c, d, k[12], 7, 1804603682);
  d = ff(d, a, b, c, k[13], 12, -40341101);
  c = ff(c, d, a, b, k[14], 17, -1502002290);
  b = ff(b, c, d, a, k[15], 22, 1236535329);
  a = gg(a, b, c, d, k[1], 5, -165796510);
  d = gg(d, a, b, c, k[6], 9, -1069501632);
  c = gg(c, d, a, b, k[11], 14, 643717713);
  b = gg(b, c, d, a, k[0], 20, -373897302);
  a = gg(a, b, c, d, k[5], 5, -701558691);
  d = gg(d, a, b, c, k[10], 9, 38016083);
  c = gg(c, d, a, b, k[15], 14, -660478335);
  b = gg(b, c, d, a, k[4], 20, -405537848);
  a = gg(a, b, c, d, k[9], 5, 568446438);
  d = gg(d, a, b, c, k[14], 9, -1019803690);
  c = gg(c, d, a, b, k[3], 14, -187363961);
  b = gg(b, c, d, a, k[8], 20, 1163531501);
  a = gg(a, b, c, d, k[13], 5, -1444681467);
  d = gg(d, a, b, c, k[2], 9, -51403784);
  c = gg(c, d, a, b, k[7], 14, 1735328473);
  b = gg(b, c, d, a, k[12], 20, -1926607734);
  a = hh(a, b, c, d, k[5], 4, -378558);
  d = hh(d, a, b, c, k[8], 11, -2022574463);
  c = hh(c, d, a, b, k[11], 16, 1839030562);
  b = hh(b, c, d, a, k[14], 23, -35309556);
  a = hh(a, b, c, d, k[1], 4, -1530992060);
  d = hh(d, a, b, c, k[4], 11, 1272893353);
  c = hh(c, d, a, b, k[7], 16, -155497632);
  b = hh(b, c, d, a, k[10], 23, -1094730640);
  a = hh(a, b, c, d, k[13], 4, 681279174);
  d = hh(d, a, b, c, k[0], 11, -358537222);
  c = hh(c, d, a, b, k[3], 16, -722521979);
  b = hh(b, c, d, a, k[6], 23, 76029189);
  a = hh(a, b, c, d, k[9], 4, -640364487);
  d = hh(d, a, b, c, k[12], 11, -421815835);
  c = hh(c, d, a, b, k[15], 16, 530742520);
  b = hh(b, c, d, a, k[2], 23, -995338651);
  a = ii(a, b, c, d, k[0], 6, -198630844);
  d = ii(d, a, b, c, k[7], 10, 1126891415);
  c = ii(c, d, a, b, k[14], 15, -1416354905);
  b = ii(b, c, d, a, k[5], 21, -57434055);
  a = ii(a, b, c, d, k[12], 6, 1700485571);
  d = ii(d, a, b, c, k[3], 10, -1894986606);
  c = ii(c, d, a, b, k[10], 15, -1051523);
  b = ii(b, c, d, a, k[1], 21, -2054922799);
  a = ii(a, b, c, d, k[8], 6, 1873313359);
  d = ii(d, a, b, c, k[15], 10, -30611744);
  c = ii(c, d, a, b, k[6], 15, -1560198380);
  b = ii(b, c, d, a, k[13], 21, 1309151649);
  a = ii(a, b, c, d, k[4], 6, -145523070);
  d = ii(d, a, b, c, k[11], 10, -1120210379);
  c = ii(c, d, a, b, k[2], 15, 718787259);
  b = ii(b, c, d, a, k[9], 21, -343485551);
  x[0] = add32(a, x[0]);
  x[1] = add32(b, x[1]);
  x[2] = add32(c, x[2]);
  x[3] = add32(d, x[3]);
}

function cmn(q, a, b, x, s, t) {
  a = add32(add32(a, q), add32(x, t));
  return add32((a << s) | (a >>> (32 - s)), b);
}

function ff(a, b, c, d, x, s, t) {
  return cmn((b & c) | (~b & d), a, b, x, s, t);
}

function gg(a, b, c, d, x, s, t) {
  return cmn((b & d) | (c & ~d), a, b, x, s, t);
}

function hh(a, b, c, d, x, s, t) {
  return cmn(b ^ c ^ d, a, b, x, s, t);
}

function ii(a, b, c, d, x, s, t) {
  return cmn(c ^ (b | ~d), a, b, x, s, t);
}

function md51(s) {
  var txt = "";
  var n = s.length,
    state = [1732584193, -271733879, -1732584194, 271733878],
    i;
  for (i = 64; i <= s.length; i += 64) {
    md5cycle(state, md5blk(s.substring(i - 64, i)));
  }
  s = s.substring(i - 64);
  var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
  for (i = 0; i < s.length; i++) tail[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
  tail[i >> 2] |= 0x80 << (i % 4 << 3);
  if (i > 55) {
    md5cycle(state, tail);
    for (i = 0; i < 16; i++) tail[i] = 0;
  }
  tail[14] = n * 8;
  md5cycle(state, tail);
  return state;
}

function md5blk(s) {
  /* I figured global was faster.   */
  var md5blks = [],
    i; /* Andy King said do it this way. */
  for (i = 0; i < 64; i += 4) {
    md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
  }
  return md5blks;
}
var hex_chr = "0123456789abcdef".split("");

function rhex(n) {
  var s = "",
    j = 0;
  for (; j < 4; j++) s += hex_chr[(n >> (j * 8 + 4)) & 0x0f] + hex_chr[(n >> (j * 8)) & 0x0f];
  return s;
}

function hex(x) {
  for (var i = 0; i < x.length; i++) x[i] = rhex(x[i]);
  return x.join("");
}

function md5(s) {
  return hex(md51(s));
}

function add32(a, b) {
  return (a + b) & 0xffffffff;
}
if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
  function add32(x, y) {
    var lsw = (x & 0xffff) + (y & 0xffff),
      msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return (msw << 16) | (lsw & 0xffff);
  }
}
(function () {
  function md5cycle(x, k) {
    var a = x[0],
      b = x[1],
      c = x[2],
      d = x[3];
    a = ff(a, b, c, d, k[0], 7, -680876936);
    d = ff(d, a, b, c, k[1], 12, -389564586);
    c = ff(c, d, a, b, k[2], 17, 606105819);
    b = ff(b, c, d, a, k[3], 22, -1044525330);
    a = ff(a, b, c, d, k[4], 7, -176418897);
    d = ff(d, a, b, c, k[5], 12, 1200080426);
    c = ff(c, d, a, b, k[6], 17, -1473231341);
    b = ff(b, c, d, a, k[7], 22, -45705983);
    a = ff(a, b, c, d, k[8], 7, 1770035416);
    d = ff(d, a, b, c, k[9], 12, -1958414417);
    c = ff(c, d, a, b, k[10], 17, -42063);
    b = ff(b, c, d, a, k[11], 22, -1990404162);
    a = ff(a, b, c, d, k[12], 7, 1804603682);
    d = ff(d, a, b, c, k[13], 12, -40341101);
    c = ff(c, d, a, b, k[14], 17, -1502002290);
    b = ff(b, c, d, a, k[15], 22, 1236535329);
    a = gg(a, b, c, d, k[1], 5, -165796510);
    d = gg(d, a, b, c, k[6], 9, -1069501632);
    c = gg(c, d, a, b, k[11], 14, 643717713);
    b = gg(b, c, d, a, k[0], 20, -373897302);
    a = gg(a, b, c, d, k[5], 5, -701558691);
    d = gg(d, a, b, c, k[10], 9, 38016083);
    c = gg(c, d, a, b, k[15], 14, -660478335);
    b = gg(b, c, d, a, k[4], 20, -405537848);
    a = gg(a, b, c, d, k[9], 5, 568446438);
    d = gg(d, a, b, c, k[14], 9, -1019803690);
    c = gg(c, d, a, b, k[3], 14, -187363961);
    b = gg(b, c, d, a, k[8], 20, 1163531501);
    a = gg(a, b, c, d, k[13], 5, -1444681467);
    d = gg(d, a, b, c, k[2], 9, -51403784);
    c = gg(c, d, a, b, k[7], 14, 1735328473);
    b = gg(b, c, d, a, k[12], 20, -1926607734);
    a = hh(a, b, c, d, k[5], 4, -378558);
    d = hh(d, a, b, c, k[8], 11, -2022574463);
    c = hh(c, d, a, b, k[11], 16, 1839030562);
    b = hh(b, c, d, a, k[14], 23, -35309556);
    a = hh(a, b, c, d, k[1], 4, -1530992060);
    d = hh(d, a, b, c, k[4], 11, 1272893353);
    c = hh(c, d, a, b, k[7], 16, -155497632);
    b = hh(b, c, d, a, k[10], 23, -1094730640);
    a = hh(a, b, c, d, k[13], 4, 681279174);
    d = hh(d, a, b, c, k[0], 11, -358537222);
    c = hh(c, d, a, b, k[3], 16, -722521979);
    b = hh(b, c, d, a, k[6], 23, 76029189);
    a = hh(a, b, c, d, k[9], 4, -640364487);
    d = hh(d, a, b, c, k[12], 11, -421815835);
    c = hh(c, d, a, b, k[15], 16, 530742520);
    b = hh(b, c, d, a, k[2], 23, -995338651);
    a = ii(a, b, c, d, k[0], 6, -198630844);
    d = ii(d, a, b, c, k[7], 10, 1126891415);
    c = ii(c, d, a, b, k[14], 15, -1416354905);
    b = ii(b, c, d, a, k[5], 21, -57434055);
    a = ii(a, b, c, d, k[12], 6, 1700485571);
    d = ii(d, a, b, c, k[3], 10, -1894986606);
    c = ii(c, d, a, b, k[10], 15, -1051523);
    b = ii(b, c, d, a, k[1], 21, -2054922799);
    a = ii(a, b, c, d, k[8], 6, 1873313359);
    d = ii(d, a, b, c, k[15], 10, -30611744);
    c = ii(c, d, a, b, k[6], 15, -1560198380);
    b = ii(b, c, d, a, k[13], 21, 1309151649);
    a = ii(a, b, c, d, k[4], 6, -145523070);
    d = ii(d, a, b, c, k[11], 10, -1120210379);
    c = ii(c, d, a, b, k[2], 15, 718787259);
    b = ii(b, c, d, a, k[9], 21, -343485551);
    x[0] = add32(a, x[0]);
    x[1] = add32(b, x[1]);
    x[2] = add32(c, x[2]);
    x[3] = add32(d, x[3]);
  }

  function cmn(q, a, b, x, s, t) {
    a = add32(add32(a, q), add32(x, t));
    return add32((a << s) | (a >>> (32 - s)), b);
  }

  function ff(a, b, c, d, x, s, t) {
    return cmn((b & c) | (~b & d), a, b, x, s, t);
  }

  function gg(a, b, c, d, x, s, t) {
    return cmn((b & d) | (c & ~d), a, b, x, s, t);
  }

  function hh(a, b, c, d, x, s, t) {
    return cmn(b ^ c ^ d, a, b, x, s, t);
  }

  function ii(a, b, c, d, x, s, t) {
    return cmn(c ^ (b | ~d), a, b, x, s, t);
  }

  function add32(a, b) {
    return (a + b) & 0xffffffff;
  }
  if (md5("hello") != "5d41402abc4b2a76b9719d911017c592") {
    function add32(x, y) {
      var lsw = (x & 0xffff) + (y & 0xffff),
        msw = (x >> 16) + (y >> 16) + (lsw >> 16);
      return (msw << 16) | (lsw & 0xffff);
    }
  }
})();

// ========================================================== md5方法end =================================================

// ========================================================== sha256方法start ================================================= 

/* SHA256 logical functions */
function rotateRight(n, x) {
  return ((x >>> n) | (x << (32 - n)));
}

function choice(x, y, z) {
  return ((x & y) ^ (~x & z));
}

function majority(x, y, z) {
  return ((x & y) ^ (x & z) ^ (y & z));
}

function sha256_Sigma0(x) {
  return (rotateRight(2, x) ^ rotateRight(13, x) ^ rotateRight(22, x));
}

function sha256_Sigma1(x) {
  return (rotateRight(6, x) ^ rotateRight(11, x) ^ rotateRight(25, x));
}

function sha256_sigma0(x) {
  return (rotateRight(7, x) ^ rotateRight(18, x) ^ (x >>> 3));
}

function sha256_sigma1(x) {
  return (rotateRight(17, x) ^ rotateRight(19, x) ^ (x >>> 10));
}

function sha256_expand(W, j) {
  return (W[j & 0x0f] += sha256_sigma1(W[(j + 14) & 0x0f]) + W[(j + 9) & 0x0f] +
    sha256_sigma0(W[(j + 1) & 0x0f]));
}

/* Hash constant words K: */
var K256 = new Array(
  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
  0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
  0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
  0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
  0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
  0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3,
  0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5,
  0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
  0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
);

/* global arrays */
var ihash, count, buffer;
var sha256_hex_digits = "0123456789abcdef";

/* Add 32-bit integers with 16-bit operations (bug in some JS-interpreters:
overflow) */
function safe_add256(x, y) {
  var lsw = (x & 0xffff) + (y & 0xffff);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return (msw << 16) | (lsw & 0xffff);
}

/* Initialise the SHA256 computation */
function sha256_init() {
  ihash = new Array(8);
  count = new Array(2);
  buffer = new Array(64);
  count[0] = count[1] = 0;
  ihash[0] = 0x6a09e667;
  ihash[1] = 0xbb67ae85;
  ihash[2] = 0x3c6ef372;
  ihash[3] = 0xa54ff53a;
  ihash[4] = 0x510e527f;
  ihash[5] = 0x9b05688c;
  ihash[6] = 0x1f83d9ab;
  ihash[7] = 0x5be0cd19;
}

/* Transform a 512-bit message block */
function sha256_transform() {
  var a, b, c, d, e, f, g, h, T1, T2;
  var W = new Array(16);

  /* Initialize registers with the previous intermediate value */
  a = ihash[0];
  b = ihash[1];
  c = ihash[2];
  d = ihash[3];
  e = ihash[4];
  f = ihash[5];
  g = ihash[6];
  h = ihash[7];

  /* make 32-bit words */
  for (var i = 0; i < 16; i++)
    W[i] = ((buffer[(i << 2) + 3]) | (buffer[(i << 2) + 2] << 8) | (buffer[(i << 2) + 1] <<
      16) | (buffer[i << 2] << 24));

  for (var j = 0; j < 64; j++) {
    T1 = h + sha256_Sigma1(e) + choice(e, f, g) + K256[j];
    if (j < 16) T1 += W[j];
    else T1 += sha256_expand(W, j);
    T2 = sha256_Sigma0(a) + majority(a, b, c);
    h = g;
    g = f;
    f = e;
    e = safe_add256(d, T1);
    d = c;
    c = b;
    b = a;
    a = safe_add256(T1, T2);
  }

  /* Compute the current intermediate hash value */
  ihash[0] += a;
  ihash[1] += b;
  ihash[2] += c;
  ihash[3] += d;
  ihash[4] += e;
  ihash[5] += f;
  ihash[6] += g;
  ihash[7] += h;
}

/* Read the next chunk of data and update the SHA256 computation */
function sha256_update(data, inputLen) {
  var i, index, curpos = 0;
  /* Compute number of bytes mod 64 */
  index = ((count[0] >> 3) & 0x3f);
  var remainder = (inputLen & 0x3f);

  /* Update number of bits */
  if ((count[0] += (inputLen << 3)) < (inputLen << 3)) count[1]++;
  count[1] += (inputLen >> 29);

  /* Transform as many times as possible */
  for (i = 0; i + 63 < inputLen; i += 64) {
    for (var j = index; j < 64; j++)
      buffer[j] = data.charCodeAt(curpos++);
    sha256_transform();
    index = 0;
  }

  /* Buffer remaining input */
  for (var j = 0; j < remainder; j++)
    buffer[j] = data.charCodeAt(curpos++);
}

/* Finish the computation by operations such as padding */
function sha256_final() {
  var index = ((count[0] >> 3) & 0x3f);
  buffer[index++] = 0x80;
  if (index <= 56) {
    for (var i = index; i < 56; i++)
      buffer[i] = 0;
  } else {
    for (var i = index; i < 64; i++)
      buffer[i] = 0;
    sha256_transform();
    for (var i = 0; i < 56; i++)
      buffer[i] = 0;
  }
  buffer[56] = (count[1] >>> 24) & 0xff;
  buffer[57] = (count[1] >>> 16) & 0xff;
  buffer[58] = (count[1] >>> 8) & 0xff;
  buffer[59] = count[1] & 0xff;
  buffer[60] = (count[0] >>> 24) & 0xff;
  buffer[61] = (count[0] >>> 16) & 0xff;
  buffer[62] = (count[0] >>> 8) & 0xff;
  buffer[63] = count[0] & 0xff;
  sha256_transform();
}

/* Split the internal hash values into an array of bytes */
function sha256_encode_bytes() {
  var j = 0;
  var output = new Array(32);
  for (var i = 0; i < 8; i++) {
    output[j++] = ((ihash[i] >>> 24) & 0xff);
    output[j++] = ((ihash[i] >>> 16) & 0xff);
    output[j++] = ((ihash[i] >>> 8) & 0xff);
    output[j++] = (ihash[i] & 0xff);
  }
  return output;
}

/* Get the internal hash as a hex string */
function sha256_encode_hex() {
  var output = new String();
  for (var i = 0; i < 8; i++) {
    for (var j = 28; j >= 0; j -= 4)
      output += sha256_hex_digits.charAt((ihash[i] >>> j) & 0x0f);
  }
  return output;
}

/* Main function: returns a hex string representing the SHA256 value of the
given data */
function sha256_digest(data) {
  sha256_init();
  sha256_update(data, data.length);
  sha256_final();
  return sha256_encode_hex();
}

/* test if the JS-interpreter is working properly */

// ========================================================== sha256方法end ================================================= 

// ========================================================== decrypt方法start =================================================

try {
  ! function (t, e) {
    if (typeof exports === "object") {
      wx.CryptoJS = module.exports = exports = e();
    } else if (typeof define === "function" && define.amd) {
      define([], e);
    } else {
      wx.CryptoJS = t.CryptoJS = e();
    }
  }(this, function () {
    var n, o, s, a, h, t, e, l, r, i, c, f, d, u, p, S, x, b, A, H, z, _, v, g, y, B, w, k, m, C, D, E, R, M, F, P, W, O, I, U = U || function (h) {
      var i;
      if ("undefined" != typeof window && window.crypto && (i = window.crypto), "undefined" != typeof self && self.crypto && (i = self.crypto), !(i = !(i = !(i = "undefined" != typeof globalThis && globalThis.crypto ? globalThis.crypto : i) && "undefined" != typeof window && window.msCrypto ? window.msCrypto : i) && "undefined" != typeof global && global.crypto ? global.crypto : i) && "function" == typeof require) try {
        i = require("crypto")
      } catch (t) {}
      var r = Object.create || function (t) {
        return e.prototype = t, t = new e, e.prototype = null, t
      };

      function e() {}
      var t = {},
        n = t.lib = {},
        o = n.Base = {
          extend: function (t) {
            var e = r(this);
            return t && e.mixIn(t), e.hasOwnProperty("init") && this.init !== e.init || (e.init = function () {
              e.$super.init.apply(this, arguments)
            }), (e.init.prototype = e).$super = this, e
          },
          create: function () {
            var t = this.extend();
            return t.init.apply(t, arguments), t
          },
          init: function () {},
          mixIn: function (t) {
            for (var e in t) t.hasOwnProperty(e) && (this[e] = t[e]);
            t.hasOwnProperty("toString") && (this.toString = t.toString)
          },
          clone: function () {
            return this.init.prototype.extend(this)
          }
        },
        l = n.WordArray = o.extend({
          init: function (t, e) {
            t = this.words = t || [], this.sigBytes = null != e ? e : 4 * t.length
          },
          toString: function (t) {
            return (t || c).stringify(this)
          },
          concat: function (t) {
            var e = this.words,
              r = t.words,
              i = this.sigBytes,
              n = t.sigBytes;
            if (this.clamp(), i % 4)
              for (var o = 0; o < n; o++) {
                var s = r[o >>> 2] >>> 24 - o % 4 * 8 & 255;
                e[i + o >>> 2] |= s << 24 - (i + o) % 4 * 8
              } else
                for (var c = 0; c < n; c += 4) e[i + c >>> 2] = r[c >>> 2];
            return this.sigBytes += n, this
          },
          clamp: function () {
            var t = this.words,
              e = this.sigBytes;
            t[e >>> 2] &= 4294967295 << 32 - e % 4 * 8, t.length = h.ceil(e / 4)
          },
          clone: function () {
            var t = o.clone.call(this);
            return t.words = this.words.slice(0), t
          },
          random: function (t) {
            for (var e = [], r = 0; r < t; r += 4) e.push(function () {
              if (i) {
                if ("function" == typeof i.getRandomValues) try {
                  return i.getRandomValues(new Uint32Array(1))[0]
                } catch (t) {}
                if ("function" == typeof i.randomBytes) try {
                  return i.randomBytes(4).readInt32LE()
                } catch (t) {}
              }
              throw new Error("Native crypto module could not be used to get secure random number.")
            }());
            return new l.init(e, t)
          }
        }),
        s = t.enc = {},
        c = s.Hex = {
          stringify: function (t) {
            for (var e = t.words, r = t.sigBytes, i = [], n = 0; n < r; n++) {
              var o = e[n >>> 2] >>> 24 - n % 4 * 8 & 255;
              i.push((o >>> 4).toString(16)), i.push((15 & o).toString(16))
            }
            return i.join("")
          },
          parse: function (t) {
            for (var e = t.length, r = [], i = 0; i < e; i += 2) r[i >>> 3] |= parseInt(t.substr(i, 2), 16) << 24 - i % 8 * 4;
            return new l.init(r, e / 2)
          }
        },
        a = s.Latin1 = {
          stringify: function (t) {
            for (var e = t.words, r = t.sigBytes, i = [], n = 0; n < r; n++) {
              var o = e[n >>> 2] >>> 24 - n % 4 * 8 & 255;
              i.push(String.fromCharCode(o))
            }
            return i.join("")
          },
          parse: function (t) {
            for (var e = t.length, r = [], i = 0; i < e; i++) r[i >>> 2] |= (255 & t.charCodeAt(i)) << 24 - i % 4 * 8;
            return new l.init(r, e)
          }
        },
        f = s.Utf8 = {
          stringify: function (t) {
            try {
              return decodeURIComponent(escape(a.stringify(t)))
            } catch (t) {
              throw new Error("Malformed UTF-8 data")
            }
          },
          parse: function (t) {
            return a.parse(unescape(encodeURIComponent(t)))
          }
        },
        d = n.BufferedBlockAlgorithm = o.extend({
          reset: function () {
            this._data = new l.init, this._nDataBytes = 0
          },
          _append: function (t) {
            "string" == typeof t && (t = f.parse(t)), this._data.concat(t), this._nDataBytes += t.sigBytes
          },
          _process: function (t) {
            var e, r = this._data,
              i = r.words,
              n = r.sigBytes,
              o = this.blockSize,
              s = n / (4 * o),
              c = (s = t ? h.ceil(s) : h.max((0 | s) - this._minBufferSize, 0)) * o,
              n = h.min(4 * c, n);
            if (c) {
              for (var a = 0; a < c; a += o) this._doProcessBlock(i, a);
              e = i.splice(0, c), r.sigBytes -= n
            }
            return new l.init(e, n)
          },
          clone: function () {
            var t = o.clone.call(this);
            return t._data = this._data.clone(), t
          },
          _minBufferSize: 0
        }),
        u = (n.Hasher = d.extend({
          cfg: o.extend(),
          init: function (t) {
            this.cfg = this.cfg.extend(t), this.reset()
          },
          reset: function () {
            d.reset.call(this), this._doReset()
          },
          update: function (t) {
            return this._append(t), this._process(), this
          },
          finalize: function (t) {
            return t && this._append(t), this._doFinalize()
          },
          blockSize: 16,
          _createHelper: function (r) {
            return function (t, e) {
              return new r.init(e).finalize(t)
            }
          },
          _createHmacHelper: function (r) {
            return function (t, e) {
              return new u.HMAC.init(r, e).finalize(t)
            }
          }
        }), t.algo = {});
      return t
    }(Math);

    function K(t, e, r) {
      return t & e | ~t & r
    }

    function X(t, e, r) {
      return t & r | e & ~r
    }

    function L(t, e) {
      return t << e | t >>> 32 - e
    }

    function j(t, e, r, i) {
      var n, o = this._iv;
      o ? (n = o.slice(0), this._iv = void 0) : n = this._prevBlock, i.encryptBlock(n, 0);
      for (var s = 0; s < r; s++) t[e + s] ^= n[s]
    }

    function T(t) {
      var e, r, i;
      return 255 == (t >> 24 & 255) ? (r = t >> 8 & 255, i = 255 & t, 255 === (e = t >> 16 & 255) ? (e = 0, 255 === r ? (r = 0, 255 === i ? i = 0 : ++i) : ++r) : ++e, t = 0, t += e << 16, t += r << 8, t += i) : t += 1 << 24, t
    }

    function N() {
      for (var t = this._X, e = this._C, r = 0; r < 8; r++) E[r] = e[r];
      e[0] = e[0] + 1295307597 + this._b | 0, e[1] = e[1] + 3545052371 + (e[0] >>> 0 < E[0] >>> 0 ? 1 : 0) | 0, e[2] = e[2] + 886263092 + (e[1] >>> 0 < E[1] >>> 0 ? 1 : 0) | 0, e[3] = e[3] + 1295307597 + (e[2] >>> 0 < E[2] >>> 0 ? 1 : 0) | 0, e[4] = e[4] + 3545052371 + (e[3] >>> 0 < E[3] >>> 0 ? 1 : 0) | 0, e[5] = e[5] + 886263092 + (e[4] >>> 0 < E[4] >>> 0 ? 1 : 0) | 0, e[6] = e[6] + 1295307597 + (e[5] >>> 0 < E[5] >>> 0 ? 1 : 0) | 0, e[7] = e[7] + 3545052371 + (e[6] >>> 0 < E[6] >>> 0 ? 1 : 0) | 0, this._b = e[7] >>> 0 < E[7] >>> 0 ? 1 : 0;
      for (r = 0; r < 8; r++) {
        var i = t[r] + e[r],
          n = 65535 & i,
          o = i >>> 16;
        R[r] = ((n * n >>> 17) + n * o >>> 15) + o * o ^ ((4294901760 & i) * i | 0) + ((65535 & i) * i | 0)
      }
      t[0] = R[0] + (R[7] << 16 | R[7] >>> 16) + (R[6] << 16 | R[6] >>> 16) | 0, t[1] = R[1] + (R[0] << 8 | R[0] >>> 24) + R[7] | 0, t[2] = R[2] + (R[1] << 16 | R[1] >>> 16) + (R[0] << 16 | R[0] >>> 16) | 0, t[3] = R[3] + (R[2] << 8 | R[2] >>> 24) + R[1] | 0, t[4] = R[4] + (R[3] << 16 | R[3] >>> 16) + (R[2] << 16 | R[2] >>> 16) | 0, t[5] = R[5] + (R[4] << 8 | R[4] >>> 24) + R[3] | 0, t[6] = R[6] + (R[5] << 16 | R[5] >>> 16) + (R[4] << 16 | R[4] >>> 16) | 0, t[7] = R[7] + (R[6] << 8 | R[6] >>> 24) + R[5] | 0
    }

    function q() {
      for (var t = this._X, e = this._C, r = 0; r < 8; r++) O[r] = e[r];
      e[0] = e[0] + 1295307597 + this._b | 0, e[1] = e[1] + 3545052371 + (e[0] >>> 0 < O[0] >>> 0 ? 1 : 0) | 0, e[2] = e[2] + 886263092 + (e[1] >>> 0 < O[1] >>> 0 ? 1 : 0) | 0, e[3] = e[3] + 1295307597 + (e[2] >>> 0 < O[2] >>> 0 ? 1 : 0) | 0, e[4] = e[4] + 3545052371 + (e[3] >>> 0 < O[3] >>> 0 ? 1 : 0) | 0, e[5] = e[5] + 886263092 + (e[4] >>> 0 < O[4] >>> 0 ? 1 : 0) | 0, e[6] = e[6] + 1295307597 + (e[5] >>> 0 < O[5] >>> 0 ? 1 : 0) | 0, e[7] = e[7] + 3545052371 + (e[6] >>> 0 < O[6] >>> 0 ? 1 : 0) | 0, this._b = e[7] >>> 0 < O[7] >>> 0 ? 1 : 0;
      for (r = 0; r < 8; r++) {
        var i = t[r] + e[r],
          n = 65535 & i,
          o = i >>> 16;
        I[r] = ((n * n >>> 17) + n * o >>> 15) + o * o ^ ((4294901760 & i) * i | 0) + ((65535 & i) * i | 0)
      }
      t[0] = I[0] + (I[7] << 16 | I[7] >>> 16) + (I[6] << 16 | I[6] >>> 16) | 0, t[1] = I[1] + (I[0] << 8 | I[0] >>> 24) + I[7] | 0, t[2] = I[2] + (I[1] << 16 | I[1] >>> 16) + (I[0] << 16 | I[0] >>> 16) | 0, t[3] = I[3] + (I[2] << 8 | I[2] >>> 24) + I[1] | 0, t[4] = I[4] + (I[3] << 16 | I[3] >>> 16) + (I[2] << 16 | I[2] >>> 16) | 0, t[5] = I[5] + (I[4] << 8 | I[4] >>> 24) + I[3] | 0, t[6] = I[6] + (I[5] << 16 | I[5] >>> 16) + (I[4] << 16 | I[4] >>> 16) | 0, t[7] = I[7] + (I[6] << 8 | I[6] >>> 24) + I[5] | 0
    }
    return F = (M = U).lib, n = F.Base, o = F.WordArray, (M = M.x64 = {}).Word = n.extend({
        init: function (t, e) {
          this.high = t, this.low = e
        }
      }), M.WordArray = n.extend({
        init: function (t, e) {
          t = this.words = t || [], this.sigBytes = null != e ? e : 8 * t.length
        },
        toX32: function () {
          for (var t = this.words, e = t.length, r = [], i = 0; i < e; i++) {
            var n = t[i];
            r.push(n.high), r.push(n.low)
          }
          return o.create(r, this.sigBytes)
        },
        clone: function () {
          for (var t = n.clone.call(this), e = t.words = this.words.slice(0), r = e.length, i = 0; i < r; i++) e[i] = e[i].clone();
          return t
        }
      }), "function" == typeof ArrayBuffer && (P = U.lib.WordArray, s = P.init, (P.init = function (t) {
        if ((t = (t = t instanceof ArrayBuffer ? new Uint8Array(t) : t) instanceof Int8Array || "undefined" != typeof Uint8ClampedArray && t instanceof Uint8ClampedArray || t instanceof Int16Array || t instanceof Uint16Array || t instanceof Int32Array || t instanceof Uint32Array || t instanceof Float32Array || t instanceof Float64Array ? new Uint8Array(t.buffer, t.byteOffset, t.byteLength) : t) instanceof Uint8Array) {
          for (var e = t.byteLength, r = [], i = 0; i < e; i++) r[i >>> 2] |= t[i] << 24 - i % 4 * 8;
          s.call(this, r, e)
        } else s.apply(this, arguments)
      }).prototype = P),
      function () {
        var t = U,
          n = t.lib.WordArray,
          t = t.enc;
        t.Utf16 = t.Utf16BE = {
          stringify: function (t) {
            for (var e = t.words, r = t.sigBytes, i = [], n = 0; n < r; n += 2) {
              var o = e[n >>> 2] >>> 16 - n % 4 * 8 & 65535;
              i.push(String.fromCharCode(o))
            }
            return i.join("")
          },
          parse: function (t) {
            for (var e = t.length, r = [], i = 0; i < e; i++) r[i >>> 1] |= t.charCodeAt(i) << 16 - i % 2 * 16;
            return n.create(r, 2 * e)
          }
        };

        function s(t) {
          return t << 8 & 4278255360 | t >>> 8 & 16711935
        }
        t.Utf16LE = {
          stringify: function (t) {
            for (var e = t.words, r = t.sigBytes, i = [], n = 0; n < r; n += 2) {
              var o = s(e[n >>> 2] >>> 16 - n % 4 * 8 & 65535);
              i.push(String.fromCharCode(o))
            }
            return i.join("")
          },
          parse: function (t) {
            for (var e = t.length, r = [], i = 0; i < e; i++) r[i >>> 1] |= s(t.charCodeAt(i) << 16 - i % 2 * 16);
            return n.create(r, 2 * e)
          }
        }
      }(), a = (w = U).lib.WordArray, w.enc.Base64 = {
        stringify: function (t) {
          var e = t.words,
            r = t.sigBytes,
            i = this._map;
          t.clamp();
          for (var n = [], o = 0; o < r; o += 3)
            for (var s = (e[o >>> 2] >>> 24 - o % 4 * 8 & 255) << 16 | (e[o + 1 >>> 2] >>> 24 - (o + 1) % 4 * 8 & 255) << 8 | e[o + 2 >>> 2] >>> 24 - (o + 2) % 4 * 8 & 255, c = 0; c < 4 && o + .75 * c < r; c++) n.push(i.charAt(s >>> 6 * (3 - c) & 63));
          var a = i.charAt(64);
          if (a)
            for (; n.length % 4;) n.push(a);
          return n.join("")
        },
        parse: function (t) {
          var e = t.length,
            r = this._map;
          if (!(i = this._reverseMap))
            for (var i = this._reverseMap = [], n = 0; n < r.length; n++) i[r.charCodeAt(n)] = n;
          var o = r.charAt(64);
          return !o || -1 !== (o = t.indexOf(o)) && (e = o),
            function (t, e, r) {
              for (var i = [], n = 0, o = 0; o < e; o++) {
                var s, c;
                o % 4 && (s = r[t.charCodeAt(o - 1)] << o % 4 * 2, c = r[t.charCodeAt(o)] >>> 6 - o % 4 * 2, c = s | c, i[n >>> 2] |= c << 24 - n % 4 * 8, n++)
              }
              return a.create(i, n)
            }(t, e, i)
        },
        _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
      }, h = (F = U).lib.WordArray, F.enc.Base64url = {
        stringify: function (t, e = !0) {
          var r = t.words,
            i = t.sigBytes,
            n = e ? this._safe_map : this._map;
          t.clamp();
          for (var o = [], s = 0; s < i; s += 3)
            for (var c = (r[s >>> 2] >>> 24 - s % 4 * 8 & 255) << 16 | (r[s + 1 >>> 2] >>> 24 - (s + 1) % 4 * 8 & 255) << 8 | r[s + 2 >>> 2] >>> 24 - (s + 2) % 4 * 8 & 255, a = 0; a < 4 && s + .75 * a < i; a++) o.push(n.charAt(c >>> 6 * (3 - a) & 63));
          var h = n.charAt(64);
          if (h)
            for (; o.length % 4;) o.push(h);
          return o.join("")
        },
        parse: function (t, e = !0) {
          var r = t.length,
            i = e ? this._safe_map : this._map;
          if (!(n = this._reverseMap))
            for (var n = this._reverseMap = [], o = 0; o < i.length; o++) n[i.charCodeAt(o)] = o;
          e = i.charAt(64);
          return !e || -1 !== (e = t.indexOf(e)) && (r = e),
            function (t, e, r) {
              for (var i = [], n = 0, o = 0; o < e; o++) {
                var s, c;
                o % 4 && (s = r[t.charCodeAt(o - 1)] << o % 4 * 2, c = r[t.charCodeAt(o)] >>> 6 - o % 4 * 2, c = s | c, i[n >>> 2] |= c << 24 - n % 4 * 8, n++)
              }
              return h.create(i, n)
            }(t, r, n)
        },
        _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
        _safe_map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
      },
      function (a) {
        var t = U,
          e = t.lib,
          r = e.WordArray,
          i = e.Hasher,
          e = t.algo,
          A = [];
        ! function () {
          for (var t = 0; t < 64; t++) A[t] = 4294967296 * a.abs(a.sin(t + 1)) | 0
        }();
        e = e.MD5 = i.extend({
          _doReset: function () {
            this._hash = new r.init([1732584193, 4023233417, 2562383102, 271733878])
          },
          _doProcessBlock: function (t, e) {
            for (var r = 0; r < 16; r++) {
              var i = e + r,
                n = t[i];
              t[i] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8)
            }
            var o = this._hash.words,
              s = t[e + 0],
              c = t[e + 1],
              a = t[e + 2],
              h = t[e + 3],
              l = t[e + 4],
              f = t[e + 5],
              d = t[e + 6],
              u = t[e + 7],
              p = t[e + 8],
              _ = t[e + 9],
              y = t[e + 10],
              v = t[e + 11],
              g = t[e + 12],
              B = t[e + 13],
              w = t[e + 14],
              k = t[e + 15],
              m = H(m = o[0], b = o[1], x = o[2], S = o[3], s, 7, A[0]),
              S = H(S, m, b, x, c, 12, A[1]),
              x = H(x, S, m, b, a, 17, A[2]),
              b = H(b, x, S, m, h, 22, A[3]);
            m = H(m, b, x, S, l, 7, A[4]), S = H(S, m, b, x, f, 12, A[5]), x = H(x, S, m, b, d, 17, A[6]), b = H(b, x, S, m, u, 22, A[7]), m = H(m, b, x, S, p, 7, A[8]), S = H(S, m, b, x, _, 12, A[9]), x = H(x, S, m, b, y, 17, A[10]), b = H(b, x, S, m, v, 22, A[11]), m = H(m, b, x, S, g, 7, A[12]), S = H(S, m, b, x, B, 12, A[13]), x = H(x, S, m, b, w, 17, A[14]), m = z(m, b = H(b, x, S, m, k, 22, A[15]), x, S, c, 5, A[16]), S = z(S, m, b, x, d, 9, A[17]), x = z(x, S, m, b, v, 14, A[18]), b = z(b, x, S, m, s, 20, A[19]), m = z(m, b, x, S, f, 5, A[20]), S = z(S, m, b, x, y, 9, A[21]), x = z(x, S, m, b, k, 14, A[22]), b = z(b, x, S, m, l, 20, A[23]), m = z(m, b, x, S, _, 5, A[24]), S = z(S, m, b, x, w, 9, A[25]), x = z(x, S, m, b, h, 14, A[26]), b = z(b, x, S, m, p, 20, A[27]), m = z(m, b, x, S, B, 5, A[28]), S = z(S, m, b, x, a, 9, A[29]), x = z(x, S, m, b, u, 14, A[30]), m = C(m, b = z(b, x, S, m, g, 20, A[31]), x, S, f, 4, A[32]), S = C(S, m, b, x, p, 11, A[33]), x = C(x, S, m, b, v, 16, A[34]), b = C(b, x, S, m, w, 23, A[35]), m = C(m, b, x, S, c, 4, A[36]), S = C(S, m, b, x, l, 11, A[37]), x = C(x, S, m, b, u, 16, A[38]), b = C(b, x, S, m, y, 23, A[39]), m = C(m, b, x, S, B, 4, A[40]), S = C(S, m, b, x, s, 11, A[41]), x = C(x, S, m, b, h, 16, A[42]), b = C(b, x, S, m, d, 23, A[43]), m = C(m, b, x, S, _, 4, A[44]), S = C(S, m, b, x, g, 11, A[45]), x = C(x, S, m, b, k, 16, A[46]), m = D(m, b = C(b, x, S, m, a, 23, A[47]), x, S, s, 6, A[48]), S = D(S, m, b, x, u, 10, A[49]), x = D(x, S, m, b, w, 15, A[50]), b = D(b, x, S, m, f, 21, A[51]), m = D(m, b, x, S, g, 6, A[52]), S = D(S, m, b, x, h, 10, A[53]), x = D(x, S, m, b, y, 15, A[54]), b = D(b, x, S, m, c, 21, A[55]), m = D(m, b, x, S, p, 6, A[56]), S = D(S, m, b, x, k, 10, A[57]), x = D(x, S, m, b, d, 15, A[58]), b = D(b, x, S, m, B, 21, A[59]), m = D(m, b, x, S, l, 6, A[60]), S = D(S, m, b, x, v, 10, A[61]), x = D(x, S, m, b, a, 15, A[62]), b = D(b, x, S, m, _, 21, A[63]), o[0] = o[0] + m | 0, o[1] = o[1] + b | 0, o[2] = o[2] + x | 0, o[3] = o[3] + S | 0
          },
          _doFinalize: function () {
            var t = this._data,
              e = t.words,
              r = 8 * this._nDataBytes,
              i = 8 * t.sigBytes;
            e[i >>> 5] |= 128 << 24 - i % 32;
            var n = a.floor(r / 4294967296),
              r = r;
            e[15 + (64 + i >>> 9 << 4)] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8), e[14 + (64 + i >>> 9 << 4)] = 16711935 & (r << 8 | r >>> 24) | 4278255360 & (r << 24 | r >>> 8), t.sigBytes = 4 * (e.length + 1), this._process();
            for (var e = this._hash, o = e.words, s = 0; s < 4; s++) {
              var c = o[s];
              o[s] = 16711935 & (c << 8 | c >>> 24) | 4278255360 & (c << 24 | c >>> 8)
            }
            return e
          },
          clone: function () {
            var t = i.clone.call(this);
            return t._hash = this._hash.clone(), t
          }
        });

        function H(t, e, r, i, n, o, s) {
          s = t + (e & r | ~e & i) + n + s;
          return (s << o | s >>> 32 - o) + e
        }

        function z(t, e, r, i, n, o, s) {
          s = t + (e & i | r & ~i) + n + s;
          return (s << o | s >>> 32 - o) + e
        }

        function C(t, e, r, i, n, o, s) {
          s = t + (e ^ r ^ i) + n + s;
          return (s << o | s >>> 32 - o) + e
        }

        function D(t, e, r, i, n, o, s) {
          s = t + (r ^ (e | ~i)) + n + s;
          return (s << o | s >>> 32 - o) + e
        }
        t.MD5 = i._createHelper(e), t.HmacMD5 = i._createHmacHelper(e)
      }(Math), P = (M = U).lib, t = P.WordArray, e = P.Hasher, P = M.algo, l = [], P = P.SHA1 = e.extend({
        _doReset: function () {
          this._hash = new t.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520])
        },
        _doProcessBlock: function (t, e) {
          for (var r = this._hash.words, i = r[0], n = r[1], o = r[2], s = r[3], c = r[4], a = 0; a < 80; a++) {
            a < 16 ? l[a] = 0 | t[e + a] : (h = l[a - 3] ^ l[a - 8] ^ l[a - 14] ^ l[a - 16], l[a] = h << 1 | h >>> 31);
            var h = (i << 5 | i >>> 27) + c + l[a];
            h += a < 20 ? 1518500249 + (n & o | ~n & s) : a < 40 ? 1859775393 + (n ^ o ^ s) : a < 60 ? (n & o | n & s | o & s) - 1894007588 : (n ^ o ^ s) - 899497514, c = s, s = o, o = n << 30 | n >>> 2, n = i, i = h
          }
          r[0] = r[0] + i | 0, r[1] = r[1] + n | 0, r[2] = r[2] + o | 0, r[3] = r[3] + s | 0, r[4] = r[4] + c | 0
        },
        _doFinalize: function () {
          var t = this._data,
            e = t.words,
            r = 8 * this._nDataBytes,
            i = 8 * t.sigBytes;
          return e[i >>> 5] |= 128 << 24 - i % 32, e[14 + (64 + i >>> 9 << 4)] = Math.floor(r / 4294967296), e[15 + (64 + i >>> 9 << 4)] = r, t.sigBytes = 4 * e.length, this._process(), this._hash
        },
        clone: function () {
          var t = e.clone.call(this);
          return t._hash = this._hash.clone(), t
        }
      }), M.SHA1 = e._createHelper(P), M.HmacSHA1 = e._createHmacHelper(P),
      function (n) {
        var t = U,
          e = t.lib,
          r = e.WordArray,
          i = e.Hasher,
          e = t.algo,
          o = [],
          p = [];
        ! function () {
          function t(t) {
            return 4294967296 * (t - (0 | t)) | 0
          }
          for (var e = 2, r = 0; r < 64;) ! function (t) {
            for (var e = n.sqrt(t), r = 2; r <= e; r++)
              if (!(t % r)) return;
            return 1
          }(e) || (r < 8 && (o[r] = t(n.pow(e, .5))), p[r] = t(n.pow(e, 1 / 3)), r++), e++
        }();
        var _ = [],
          e = e.SHA256 = i.extend({
            _doReset: function () {
              this._hash = new r.init(o.slice(0))
            },
            _doProcessBlock: function (t, e) {
              for (var r = this._hash.words, i = r[0], n = r[1], o = r[2], s = r[3], c = r[4], a = r[5], h = r[6], l = r[7], f = 0; f < 64; f++) {
                f < 16 ? _[f] = 0 | t[e + f] : (d = _[f - 15], u = _[f - 2], _[f] = ((d << 25 | d >>> 7) ^ (d << 14 | d >>> 18) ^ d >>> 3) + _[f - 7] + ((u << 15 | u >>> 17) ^ (u << 13 | u >>> 19) ^ u >>> 10) + _[f - 16]);
                var d = i & n ^ i & o ^ n & o,
                  u = l + ((c << 26 | c >>> 6) ^ (c << 21 | c >>> 11) ^ (c << 7 | c >>> 25)) + (c & a ^ ~c & h) + p[f] + _[f],
                  l = h,
                  h = a,
                  a = c,
                  c = s + u | 0,
                  s = o,
                  o = n,
                  n = i,
                  i = u + (((i << 30 | i >>> 2) ^ (i << 19 | i >>> 13) ^ (i << 10 | i >>> 22)) + d) | 0
              }
              r[0] = r[0] + i | 0, r[1] = r[1] + n | 0, r[2] = r[2] + o | 0, r[3] = r[3] + s | 0, r[4] = r[4] + c | 0, r[5] = r[5] + a | 0, r[6] = r[6] + h | 0, r[7] = r[7] + l | 0
            },
            _doFinalize: function () {
              var t = this._data,
                e = t.words,
                r = 8 * this._nDataBytes,
                i = 8 * t.sigBytes;
              return e[i >>> 5] |= 128 << 24 - i % 32, e[14 + (64 + i >>> 9 << 4)] = n.floor(r / 4294967296), e[15 + (64 + i >>> 9 << 4)] = r, t.sigBytes = 4 * e.length, this._process(), this._hash
            },
            clone: function () {
              var t = i.clone.call(this);
              return t._hash = this._hash.clone(), t
            }
          });
        t.SHA256 = i._createHelper(e), t.HmacSHA256 = i._createHmacHelper(e)
      }(Math), r = (w = U).lib.WordArray, F = w.algo, i = F.SHA256, F = F.SHA224 = i.extend({
        _doReset: function () {
          this._hash = new r.init([3238371032, 914150663, 812702999, 4144912697, 4290775857, 1750603025, 1694076839, 3204075428])
        },
        _doFinalize: function () {
          var t = i._doFinalize.call(this);
          return t.sigBytes -= 4, t
        }
      }), w.SHA224 = i._createHelper(F), w.HmacSHA224 = i._createHmacHelper(F),
      function () {
        var t = U,
          e = t.lib.Hasher,
          r = t.x64,
          i = r.Word,
          n = r.WordArray,
          r = t.algo;

        function o() {
          return i.create.apply(i, arguments)
        }
        var t1 = [o(1116352408, 3609767458), o(1899447441, 602891725), o(3049323471, 3964484399), o(3921009573, 2173295548), o(961987163, 4081628472), o(1508970993, 3053834265), o(2453635748, 2937671579), o(2870763221, 3664609560), o(3624381080, 2734883394), o(310598401, 1164996542), o(607225278, 1323610764), o(1426881987, 3590304994), o(1925078388, 4068182383), o(2162078206, 991336113), o(2614888103, 633803317), o(3248222580, 3479774868), o(3835390401, 2666613458), o(4022224774, 944711139), o(264347078, 2341262773), o(604807628, 2007800933), o(770255983, 1495990901), o(1249150122, 1856431235), o(1555081692, 3175218132), o(1996064986, 2198950837), o(2554220882, 3999719339), o(2821834349, 766784016), o(2952996808, 2566594879), o(3210313671, 3203337956), o(3336571891, 1034457026), o(3584528711, 2466948901), o(113926993, 3758326383), o(338241895, 168717936), o(666307205, 1188179964), o(773529912, 1546045734), o(1294757372, 1522805485), o(1396182291, 2643833823), o(1695183700, 2343527390), o(1986661051, 1014477480), o(2177026350, 1206759142), o(2456956037, 344077627), o(2730485921, 1290863460), o(2820302411, 3158454273), o(3259730800, 3505952657), o(3345764771, 106217008), o(3516065817, 3606008344), o(3600352804, 1432725776), o(4094571909, 1467031594), o(275423344, 851169720), o(430227734, 3100823752), o(506948616, 1363258195), o(659060556, 3750685593), o(883997877, 3785050280), o(958139571, 3318307427), o(1322822218, 3812723403), o(1537002063, 2003034995), o(1747873779, 3602036899), o(1955562222, 1575990012), o(2024104815, 1125592928), o(2227730452, 2716904306), o(2361852424, 442776044), o(2428436474, 593698344), o(2756734187, 3733110249), o(3204031479, 2999351573), o(3329325298, 3815920427), o(3391569614, 3928383900), o(3515267271, 566280711), o(3940187606, 3454069534), o(4118630271, 4000239992), o(116418474, 1914138554), o(174292421, 2731055270), o(289380356, 3203993006), o(460393269, 320620315), o(685471733, 587496836), o(852142971, 1086792851), o(1017036298, 365543100), o(1126000580, 2618297676), o(1288033470, 3409855158), o(1501505948, 4234509866), o(1607167915, 987167468), o(1816402316, 1246189591)],
          e1 = [];
        ! function () {
          for (var t = 0; t < 80; t++) e1[t] = o()
        }();
        r = r.SHA512 = e.extend({
          _doReset: function () {
            this._hash = new n.init([new i.init(1779033703, 4089235720), new i.init(3144134277, 2227873595), new i.init(1013904242, 4271175723), new i.init(2773480762, 1595750129), new i.init(1359893119, 2917565137), new i.init(2600822924, 725511199), new i.init(528734635, 4215389547), new i.init(1541459225, 327033209)])
          },
          _doProcessBlock: function (t, e) {
            for (var r = this._hash.words, i = r[0], n = r[1], o = r[2], s = r[3], c = r[4], a = r[5], h = r[6], l = r[7], f = i.high, d = i.low, u = n.high, p = n.low, _ = o.high, y = o.low, v = s.high, g = s.low, B = c.high, w = c.low, k = a.high, m = a.low, S = h.high, x = h.low, b = l.high, r = l.low, A = f, H = d, z = u, C = p, D = _, E = y, R = v, M = g, F = B, P = w, W = k, O = m, I = S, U = x, K = b, X = r, L = 0; L < 80; L++) {
              var j, T, N = e1[L];
              L < 16 ? (T = N.high = 0 | t[e + 2 * L], j = N.low = 0 | t[e + 2 * L + 1]) : ($ = (q = e1[L - 15]).high, J = q.low, G = (Q = e1[L - 2]).high, V = Q.low, Z = (Y = e1[L - 7]).high, q = Y.low, Y = (Q = e1[L - 16]).high, T = (T = (($ >>> 1 | J << 31) ^ ($ >>> 8 | J << 24) ^ $ >>> 7) + Z + ((j = (Z = (J >>> 1 | $ << 31) ^ (J >>> 8 | $ << 24) ^ (J >>> 7 | $ << 25)) + q) >>> 0 < Z >>> 0 ? 1 : 0)) + ((G >>> 19 | V << 13) ^ (G << 3 | V >>> 29) ^ G >>> 6) + ((j += J = (V >>> 19 | G << 13) ^ (V << 3 | G >>> 29) ^ (V >>> 6 | G << 26)) >>> 0 < J >>> 0 ? 1 : 0), j += $ = Q.low, N.high = T = T + Y + (j >>> 0 < $ >>> 0 ? 1 : 0), N.low = j);
              var q = F & W ^ ~F & I,
                Z = P & O ^ ~P & U,
                V = A & z ^ A & D ^ z & D,
                G = (H >>> 28 | A << 4) ^ (H << 30 | A >>> 2) ^ (H << 25 | A >>> 7),
                J = t1[L],
                Q = J.high,
                Y = J.low,
                $ = X + ((P >>> 14 | F << 18) ^ (P >>> 18 | F << 14) ^ (P << 23 | F >>> 9)),
                N = K + ((F >>> 14 | P << 18) ^ (F >>> 18 | P << 14) ^ (F << 23 | P >>> 9)) + ($ >>> 0 < X >>> 0 ? 1 : 0),
                J = G + (H & C ^ H & E ^ C & E),
                K = I,
                X = U,
                I = W,
                U = O,
                W = F,
                O = P,
                F = R + (N = (N = (N = N + q + (($ = $ + Z) >>> 0 < Z >>> 0 ? 1 : 0)) + Q + (($ = $ + Y) >>> 0 < Y >>> 0 ? 1 : 0)) + T + (($ = $ + j) >>> 0 < j >>> 0 ? 1 : 0)) + ((P = M + $ | 0) >>> 0 < M >>> 0 ? 1 : 0) | 0,
                R = D,
                M = E,
                D = z,
                E = C,
                z = A,
                C = H,
                A = N + (((A >>> 28 | H << 4) ^ (A << 30 | H >>> 2) ^ (A << 25 | H >>> 7)) + V + (J >>> 0 < G >>> 0 ? 1 : 0)) + ((H = $ + J | 0) >>> 0 < $ >>> 0 ? 1 : 0) | 0
            }
            d = i.low = d + H, i.high = f + A + (d >>> 0 < H >>> 0 ? 1 : 0), p = n.low = p + C, n.high = u + z + (p >>> 0 < C >>> 0 ? 1 : 0), y = o.low = y + E, o.high = _ + D + (y >>> 0 < E >>> 0 ? 1 : 0), g = s.low = g + M, s.high = v + R + (g >>> 0 < M >>> 0 ? 1 : 0), w = c.low = w + P, c.high = B + F + (w >>> 0 < P >>> 0 ? 1 : 0), m = a.low = m + O, a.high = k + W + (m >>> 0 < O >>> 0 ? 1 : 0), x = h.low = x + U, h.high = S + I + (x >>> 0 < U >>> 0 ? 1 : 0), r = l.low = r + X, l.high = b + K + (r >>> 0 < X >>> 0 ? 1 : 0)
          },
          _doFinalize: function () {
            var t = this._data,
              e = t.words,
              r = 8 * this._nDataBytes,
              i = 8 * t.sigBytes;
            return e[i >>> 5] |= 128 << 24 - i % 32, e[30 + (128 + i >>> 10 << 5)] = Math.floor(r / 4294967296), e[31 + (128 + i >>> 10 << 5)] = r, t.sigBytes = 4 * e.length, this._process(), this._hash.toX32()
          },
          clone: function () {
            var t = e.clone.call(this);
            return t._hash = this._hash.clone(), t
          },
          blockSize: 32
        });
        t.SHA512 = e._createHelper(r), t.HmacSHA512 = e._createHmacHelper(r)
      }(), P = (M = U).x64, c = P.Word, f = P.WordArray, P = M.algo, d = P.SHA512, P = P.SHA384 = d.extend({
        _doReset: function () {
          this._hash = new f.init([new c.init(3418070365, 3238371032), new c.init(1654270250, 914150663), new c.init(2438529370, 812702999), new c.init(355462360, 4144912697), new c.init(1731405415, 4290775857), new c.init(2394180231, 1750603025), new c.init(3675008525, 1694076839), new c.init(1203062813, 3204075428)])
        },
        _doFinalize: function () {
          var t = d._doFinalize.call(this);
          return t.sigBytes -= 16, t
        }
      }), M.SHA384 = d._createHelper(P), M.HmacSHA384 = d._createHmacHelper(P),
      function (l) {
        var t = U,
          e = t.lib,
          f = e.WordArray,
          i = e.Hasher,
          d = t.x64.Word,
          e = t.algo,
          A = [],
          H = [],
          z = [];
        ! function () {
          for (var t = 1, e = 0, r = 0; r < 24; r++) {
            A[t + 5 * e] = (r + 1) * (r + 2) / 2 % 64;
            var i = (2 * t + 3 * e) % 5;
            t = e % 5, e = i
          }
          for (t = 0; t < 5; t++)
            for (e = 0; e < 5; e++) H[t + 5 * e] = e + (2 * t + 3 * e) % 5 * 5;
          for (var n = 1, o = 0; o < 24; o++) {
            for (var s, c = 0, a = 0, h = 0; h < 7; h++) 1 & n && ((s = (1 << h) - 1) < 32 ? a ^= 1 << s : c ^= 1 << s - 32), 128 & n ? n = n << 1 ^ 113 : n <<= 1;
            z[o] = d.create(c, a)
          }
        }();
        var C = [];
        ! function () {
          for (var t = 0; t < 25; t++) C[t] = d.create()
        }();
        e = e.SHA3 = i.extend({
          cfg: i.cfg.extend({
            outputLength: 512
          }),
          _doReset: function () {
            for (var t = this._state = [], e = 0; e < 25; e++) t[e] = new d.init;
            this.blockSize = (1600 - 2 * this.cfg.outputLength) / 32
          },
          _doProcessBlock: function (t, e) {
            for (var r = this._state, i = this.blockSize / 2, n = 0; n < i; n++) {
              var o = t[e + 2 * n],
                s = t[e + 2 * n + 1],
                o = 16711935 & (o << 8 | o >>> 24) | 4278255360 & (o << 24 | o >>> 8);
              (m = r[n]).high ^= s = 16711935 & (s << 8 | s >>> 24) | 4278255360 & (s << 24 | s >>> 8), m.low ^= o
            }
            for (var c = 0; c < 24; c++) {
              for (var a = 0; a < 5; a++) {
                for (var h = 0, l = 0, f = 0; f < 5; f++) h ^= (m = r[a + 5 * f]).high, l ^= m.low;
                var d = C[a];
                d.high = h, d.low = l
              }
              for (a = 0; a < 5; a++)
                for (var u = C[(a + 4) % 5], p = C[(a + 1) % 5], _ = p.high, p = p.low, h = u.high ^ (_ << 1 | p >>> 31), l = u.low ^ (p << 1 | _ >>> 31), f = 0; f < 5; f++)(m = r[a + 5 * f]).high ^= h, m.low ^= l;
              for (var y = 1; y < 25; y++) {
                var v = (m = r[y]).high,
                  g = m.low,
                  B = A[y];
                l = B < 32 ? (h = v << B | g >>> 32 - B, g << B | v >>> 32 - B) : (h = g << B - 32 | v >>> 64 - B, v << B - 32 | g >>> 64 - B);
                B = C[H[y]];
                B.high = h, B.low = l
              }
              var w = C[0],
                k = r[0];
              w.high = k.high, w.low = k.low;
              for (a = 0; a < 5; a++)
                for (f = 0; f < 5; f++) {
                  var m = r[y = a + 5 * f],
                    S = C[y],
                    x = C[(a + 1) % 5 + 5 * f],
                    b = C[(a + 2) % 5 + 5 * f];
                  m.high = S.high ^ ~x.high & b.high, m.low = S.low ^ ~x.low & b.low
                }
              m = r[0], k = z[c];
              m.high ^= k.high, m.low ^= k.low
            }
          },
          _doFinalize: function () {
            var t = this._data,
              e = t.words,
              r = (this._nDataBytes, 8 * t.sigBytes),
              i = 32 * this.blockSize;
            e[r >>> 5] |= 1 << 24 - r % 32, e[(l.ceil((1 + r) / i) * i >>> 5) - 1] |= 128, t.sigBytes = 4 * e.length, this._process();
            for (var n = this._state, e = this.cfg.outputLength / 8, o = e / 8, s = [], c = 0; c < o; c++) {
              var a = n[c],
                h = a.high,
                a = a.low,
                h = 16711935 & (h << 8 | h >>> 24) | 4278255360 & (h << 24 | h >>> 8);
              s.push(a = 16711935 & (a << 8 | a >>> 24) | 4278255360 & (a << 24 | a >>> 8)), s.push(h)
            }
            return new f.init(s, e)
          },
          clone: function () {
            for (var t = i.clone.call(this), e = t._state = this._state.slice(0), r = 0; r < 25; r++) e[r] = e[r].clone();
            return t
          }
        });
        t.SHA3 = i._createHelper(e), t.HmacSHA3 = i._createHmacHelper(e)
      }(Math), Math, F = (w = U).lib, u = F.WordArray, p = F.Hasher, F = w.algo, S = u.create([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]), x = u.create([5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]), b = u.create([11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]), A = u.create([8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]), H = u.create([0, 1518500249, 1859775393, 2400959708, 2840853838]), z = u.create([1352829926, 1548603684, 1836072691, 2053994217, 0]), F = F.RIPEMD160 = p.extend({
        _doReset: function () {
          this._hash = u.create([1732584193, 4023233417, 2562383102, 271733878, 3285377520])
        },
        _doProcessBlock: function (t, e) {
          for (var r = 0; r < 16; r++) {
            var i = e + r,
              n = t[i];
            t[i] = 16711935 & (n << 8 | n >>> 24) | 4278255360 & (n << 24 | n >>> 8)
          }
          for (var o, s, c, a, h, l, f = this._hash.words, d = H.words, u = z.words, p = S.words, _ = x.words, y = b.words, v = A.words, g = o = f[0], B = s = f[1], w = c = f[2], k = a = f[3], m = h = f[4], r = 0; r < 80; r += 1) l = o + t[e + p[r]] | 0, l += r < 16 ? (s ^ c ^ a) + d[0] : r < 32 ? K(s, c, a) + d[1] : r < 48 ? ((s | ~c) ^ a) + d[2] : r < 64 ? X(s, c, a) + d[3] : (s ^ (c | ~a)) + d[4], l = (l = L(l |= 0, y[r])) + h | 0, o = h, h = a, a = L(c, 10), c = s, s = l, l = g + t[e + _[r]] | 0, l += r < 16 ? (B ^ (w | ~k)) + u[0] : r < 32 ? X(B, w, k) + u[1] : r < 48 ? ((B | ~w) ^ k) + u[2] : r < 64 ? K(B, w, k) + u[3] : (B ^ w ^ k) + u[4], l = (l = L(l |= 0, v[r])) + m | 0, g = m, m = k, k = L(w, 10), w = B, B = l;
          l = f[1] + c + k | 0, f[1] = f[2] + a + m | 0, f[2] = f[3] + h + g | 0, f[3] = f[4] + o + B | 0, f[4] = f[0] + s + w | 0, f[0] = l
        },
        _doFinalize: function () {
          var t = this._data,
            e = t.words,
            r = 8 * this._nDataBytes,
            i = 8 * t.sigBytes;
          e[i >>> 5] |= 128 << 24 - i % 32, e[14 + (64 + i >>> 9 << 4)] = 16711935 & (r << 8 | r >>> 24) | 4278255360 & (r << 24 | r >>> 8), t.sigBytes = 4 * (e.length + 1), this._process();
          for (var e = this._hash, n = e.words, o = 0; o < 5; o++) {
            var s = n[o];
            n[o] = 16711935 & (s << 8 | s >>> 24) | 4278255360 & (s << 24 | s >>> 8)
          }
          return e
        },
        clone: function () {
          var t = p.clone.call(this);
          return t._hash = this._hash.clone(), t
        }
      }), w.RIPEMD160 = p._createHelper(F), w.HmacRIPEMD160 = p._createHmacHelper(F), P = (M = U).lib.Base, _ = M.enc.Utf8, M.algo.HMAC = P.extend({
        init: function (t, e) {
          t = this._hasher = new t.init, "string" == typeof e && (e = _.parse(e));
          var r = t.blockSize,
            i = 4 * r;
          (e = e.sigBytes > i ? t.finalize(e) : e).clamp();
          for (var t = this._oKey = e.clone(), e = this._iKey = e.clone(), n = t.words, o = e.words, s = 0; s < r; s++) n[s] ^= 1549556828, o[s] ^= 909522486;
          t.sigBytes = e.sigBytes = i, this.reset()
        },
        reset: function () {
          var t = this._hasher;
          t.reset(), t.update(this._iKey)
        },
        update: function (t) {
          return this._hasher.update(t), this
        },
        finalize: function (t) {
          var e = this._hasher,
            t = e.finalize(t);
          return e.reset(), e.finalize(this._oKey.clone().concat(t))
        }
      }), F = (w = U).lib, M = F.Base, v = F.WordArray, P = w.algo, F = P.SHA1, g = P.HMAC, y = P.PBKDF2 = M.extend({
        cfg: M.extend({
          keySize: 4,
          hasher: F,
          iterations: 1
        }),
        init: function (t) {
          this.cfg = this.cfg.extend(t)
        },
        compute: function (t, e) {
          for (var r = this.cfg, i = g.create(r.hasher, t), n = v.create(), o = v.create([1]), s = n.words, c = o.words, a = r.keySize, h = r.iterations; s.length < a;) {
            var l = i.update(e).finalize(o);
            i.reset();
            for (var f = l.words, d = f.length, u = l, p = 1; p < h; p++) {
              u = i.finalize(u), i.reset();
              for (var _ = u.words, y = 0; y < d; y++) f[y] ^= _[y]
            }
            n.concat(l), c[0]++
          }
          return n.sigBytes = 4 * a, n
        }
      }), w.PBKDF2 = function (t, e, r) {
        return y.create(r).compute(t, e)
      }, M = (P = U).lib, F = M.Base, B = M.WordArray, w = P.algo, M = w.MD5, k = w.EvpKDF = F.extend({
        cfg: F.extend({
          keySize: 4,
          hasher: M,
          iterations: 1
        }),
        init: function (t) {
          this.cfg = this.cfg.extend(t)
        },
        compute: function (t, e) {
          for (var r, i = this.cfg, n = i.hasher.create(), o = B.create(), s = o.words, c = i.keySize, a = i.iterations; s.length < c;) {
            r && n.update(r), r = n.update(t).finalize(e), n.reset();
            for (var h = 1; h < a; h++) r = n.finalize(r), n.reset();
            o.concat(r)
          }
          return o.sigBytes = 4 * c, o
        }
      }), P.EvpKDF = function (t, e, r) {
        return k.create(r).compute(t, e)
      }, U.lib.Cipher || function () {
        var t = U,
          e = t.lib,
          r = e.Base,
          s = e.WordArray,
          i = e.BufferedBlockAlgorithm,
          n = t.enc,
          o = (n.Utf8, n.Base64),
          c = t.algo.EvpKDF,
          a = e.Cipher = i.extend({
            cfg: r.extend(),
            createEncryptor: function (t, e) {
              return this.create(this._ENC_XFORM_MODE, t, e)
            },
            createDecryptor: function (t, e) {
              return this.create(this._DEC_XFORM_MODE, t, e)
            },
            init: function (t, e, r) {
              this.cfg = this.cfg.extend(r), this._xformMode = t, this._key = e, this.reset()
            },
            reset: function () {
              i.reset.call(this), this._doReset()
            },
            process: function (t) {
              return this._append(t), this._process()
            },
            finalize: function (t) {
              return t && this._append(t), this._doFinalize()
            },
            keySize: 4,
            ivSize: 4,
            _ENC_XFORM_MODE: 1,
            _DEC_XFORM_MODE: 2,
            _createHelper: function (i) {
              return {
                encrypt: function (t, e, r) {
                  return h(e).encrypt(i, t, e, r)
                },
                decrypt: function (t, e, r) {
                  return h(e).decrypt(i, t, e, r)
                }
              }
            }
          });

        function h(t) {
          return "string" == typeof t ? p : u
        }
        e.StreamCipher = a.extend({
          _doFinalize: function () {
            return this._process(!0)
          },
          blockSize: 1
        });
        var l = t.mode = {},
          n = e.BlockCipherMode = r.extend({
            createEncryptor: function (t, e) {
              return this.Encryptor.create(t, e)
            },
            createDecryptor: function (t, e) {
              return this.Decryptor.create(t, e)
            },
            init: function (t, e) {
              this._cipher = t, this._iv = e
            }
          }),
          n = l.CBC = ((l = n.extend()).Encryptor = l.extend({
            processBlock: function (t, e) {
              var r = this._cipher,
                i = r.blockSize;
              f.call(this, t, e, i), r.encryptBlock(t, e), this._prevBlock = t.slice(e, e + i)
            }
          }), l.Decryptor = l.extend({
            processBlock: function (t, e) {
              var r = this._cipher,
                i = r.blockSize,
                n = t.slice(e, e + i);
              r.decryptBlock(t, e), f.call(this, t, e, i), this._prevBlock = n
            }
          }), l);

        function f(t, e, r) {
          var i, n = this._iv;
          n ? (i = n, this._iv = void 0) : i = this._prevBlock;
          for (var o = 0; o < r; o++) t[e + o] ^= i[o]
        }
        var l = (t.pad = {}).Pkcs7 = {
            pad: function (t, e) {
              for (var e = 4 * e, r = e - t.sigBytes % e, i = r << 24 | r << 16 | r << 8 | r, n = [], o = 0; o < r; o += 4) n.push(i);
              e = s.create(n, r);
              t.concat(e)
            },
            unpad: function (t) {
              var e = 255 & t.words[t.sigBytes - 1 >>> 2];
              t.sigBytes -= e
            }
          },
          d = (e.BlockCipher = a.extend({
            cfg: a.cfg.extend({
              mode: n,
              padding: l
            }),
            reset: function () {
              var t;
              a.reset.call(this);
              var e = this.cfg,
                r = e.iv,
                e = e.mode;
              this._xformMode == this._ENC_XFORM_MODE ? t = e.createEncryptor : (t = e.createDecryptor, this._minBufferSize = 1), this._mode && this._mode.__creator == t ? this._mode.init(this, r && r.words) : (this._mode = t.call(e, this, r && r.words), this._mode.__creator = t)
            },
            _doProcessBlock: function (t, e) {
              this._mode.processBlock(t, e)
            },
            _doFinalize: function () {
              var t, e = this.cfg.padding;
              return this._xformMode == this._ENC_XFORM_MODE ? (e.pad(this._data, this.blockSize), t = this._process(!0)) : (t = this._process(!0), e.unpad(t)), t
            },
            blockSize: 4
          }), e.CipherParams = r.extend({
            init: function (t) {
              this.mixIn(t)
            },
            toString: function (t) {
              return (t || this.formatter).stringify(this)
            }
          })),
          l = (t.format = {}).OpenSSL = {
            stringify: function (t) {
              var e = t.ciphertext,
                t = t.salt,
                e = t ? s.create([1398893684, 1701076831]).concat(t).concat(e) : e;
              return e.toString(o)
            },
            parse: function (t) {
              var e, r = o.parse(t),
                t = r.words;
              return 1398893684 == t[0] && 1701076831 == t[1] && (e = s.create(t.slice(2, 4)), t.splice(0, 4), r.sigBytes -= 16), d.create({
                ciphertext: r,
                salt: e
              })
            }
          },
          u = e.SerializableCipher = r.extend({
            cfg: r.extend({
              format: l
            }),
            encrypt: function (t, e, r, i) {
              i = this.cfg.extend(i);
              var n = t.createEncryptor(r, i),
                e = n.finalize(e),
                n = n.cfg;
              return d.create({
                ciphertext: e,
                key: r,
                iv: n.iv,
                algorithm: t,
                mode: n.mode,
                padding: n.padding,
                blockSize: t.blockSize,
                formatter: i.format
              })
            },
            decrypt: function (t, e, r, i) {
              return i = this.cfg.extend(i), e = this._parse(e, i.format), t.createDecryptor(r, i).finalize(e.ciphertext)
            },
            _parse: function (t, e) {
              return "string" == typeof t ? e.parse(t, this) : t
            }
          }),
          t = (t.kdf = {}).OpenSSL = {
            execute: function (t, e, r, i) {
              i = i || s.random(8);
              t = c.create({
                keySize: e + r
              }).compute(t, i), r = s.create(t.words.slice(e), 4 * r);
              return t.sigBytes = 4 * e, d.create({
                key: t,
                iv: r,
                salt: i
              })
            }
          },
          p = e.PasswordBasedCipher = u.extend({
            cfg: u.cfg.extend({
              kdf: t
            }),
            encrypt: function (t, e, r, i) {
              r = (i = this.cfg.extend(i)).kdf.execute(r, t.keySize, t.ivSize);
              i.iv = r.iv;
              i = u.encrypt.call(this, t, e, r.key, i);
              return i.mixIn(r), i
            },
            decrypt: function (t, e, r, i) {
              i = this.cfg.extend(i), e = this._parse(e, i.format);
              r = i.kdf.execute(r, t.keySize, t.ivSize, e.salt);
              return i.iv = r.iv, u.decrypt.call(this, t, e, r.key, i)
            }
          })
      }(), U.mode.CFB = ((F = U.lib.BlockCipherMode.extend()).Encryptor = F.extend({
        processBlock: function (t, e) {
          var r = this._cipher,
            i = r.blockSize;
          j.call(this, t, e, i, r), this._prevBlock = t.slice(e, e + i)
        }
      }), F.Decryptor = F.extend({
        processBlock: function (t, e) {
          var r = this._cipher,
            i = r.blockSize,
            n = t.slice(e, e + i);
          j.call(this, t, e, i, r), this._prevBlock = n
        }
      }), F), U.mode.CTR = (M = U.lib.BlockCipherMode.extend(), P = M.Encryptor = M.extend({
        processBlock: function (t, e) {
          var r = this._cipher,
            i = r.blockSize,
            n = this._iv,
            o = this._counter;
          n && (o = this._counter = n.slice(0), this._iv = void 0);
          var s = o.slice(0);
          r.encryptBlock(s, 0), o[i - 1] = o[i - 1] + 1 | 0;
          for (var c = 0; c < i; c++) t[e + c] ^= s[c]
        }
      }), M.Decryptor = P, M), U.mode.CTRGladman = (F = U.lib.BlockCipherMode.extend(), P = F.Encryptor = F.extend({
        processBlock: function (t, e) {
          var r = this._cipher,
            i = r.blockSize,
            n = this._iv,
            o = this._counter;
          n && (o = this._counter = n.slice(0), this._iv = void 0), 0 === ((n = o)[0] = T(n[0])) && (n[1] = T(n[1]));
          var s = o.slice(0);
          r.encryptBlock(s, 0);
          for (var c = 0; c < i; c++) t[e + c] ^= s[c]
        }
      }), F.Decryptor = P, F), U.mode.OFB = (M = U.lib.BlockCipherMode.extend(), P = M.Encryptor = M.extend({
        processBlock: function (t, e) {
          var r = this._cipher,
            i = r.blockSize,
            n = this._iv,
            o = this._keystream;
          n && (o = this._keystream = n.slice(0), this._iv = void 0), r.encryptBlock(o, 0);
          for (var s = 0; s < i; s++) t[e + s] ^= o[s]
        }
      }), M.Decryptor = P, M), U.mode.ECB = ((F = U.lib.BlockCipherMode.extend()).Encryptor = F.extend({
        processBlock: function (t, e) {
          this._cipher.encryptBlock(t, e)
        }
      }), F.Decryptor = F.extend({
        processBlock: function (t, e) {
          this._cipher.decryptBlock(t, e)
        }
      }), F), U.pad.AnsiX923 = {
        pad: function (t, e) {
          var r = t.sigBytes,
            e = 4 * e,
            e = e - r % e,
            r = r + e - 1;
          t.clamp(), t.words[r >>> 2] |= e << 24 - r % 4 * 8, t.sigBytes += e
        },
        unpad: function (t) {
          var e = 255 & t.words[t.sigBytes - 1 >>> 2];
          t.sigBytes -= e
        }
      }, U.pad.Iso10126 = {
        pad: function (t, e) {
          e *= 4, e -= t.sigBytes % e;
          t.concat(U.lib.WordArray.random(e - 1)).concat(U.lib.WordArray.create([e << 24], 1))
        },
        unpad: function (t) {
          var e = 255 & t.words[t.sigBytes - 1 >>> 2];
          t.sigBytes -= e
        }
      }, U.pad.Iso97971 = {
        pad: function (t, e) {
          t.concat(U.lib.WordArray.create([2147483648], 1)), U.pad.ZeroPadding.pad(t, e)
        },
        unpad: function (t) {
          U.pad.ZeroPadding.unpad(t), t.sigBytes--
        }
      }, U.pad.ZeroPadding = {
        pad: function (t, e) {
          e *= 4;
          t.clamp(), t.sigBytes += e - (t.sigBytes % e || e)
        },
        unpad: function (t) {
          for (var e = t.words, r = t.sigBytes - 1, r = t.sigBytes - 1; 0 <= r; r--)
            if (e[r >>> 2] >>> 24 - r % 4 * 8 & 255) {
              t.sigBytes = r + 1;
              break
            }
        }
      }, U.pad.NoPadding = {
        pad: function () {},
        unpad: function () {}
      }, m = (P = U).lib.CipherParams, C = P.enc.Hex, P.format.Hex = {
        stringify: function (t) {
          return t.ciphertext.toString(C)
        },
        parse: function (t) {
          t = C.parse(t);
          return m.create({
            ciphertext: t
          })
        }
      },
      function () {
        var t = U,
          e = t.lib.BlockCipher,
          r = t.algo,
          h = [],
          l = [],
          f = [],
          d = [],
          u = [],
          p = [],
          _ = [],
          y = [],
          v = [],
          g = [];
        ! function () {
          for (var t = [], e = 0; e < 256; e++) t[e] = e < 128 ? e << 1 : e << 1 ^ 283;
          for (var r = 0, i = 0, e = 0; e < 256; e++) {
            var n = i ^ i << 1 ^ i << 2 ^ i << 3 ^ i << 4;
            h[r] = n = n >>> 8 ^ 255 & n ^ 99;
            var o = t[l[n] = r],
              s = t[o],
              c = t[s],
              a = 257 * t[n] ^ 16843008 * n;
            f[r] = a << 24 | a >>> 8, d[r] = a << 16 | a >>> 16, u[r] = a << 8 | a >>> 24, p[r] = a, _[n] = (a = 16843009 * c ^ 65537 * s ^ 257 * o ^ 16843008 * r) << 24 | a >>> 8, y[n] = a << 16 | a >>> 16, v[n] = a << 8 | a >>> 24, g[n] = a, r ? (r = o ^ t[t[t[c ^ o]]], i ^= t[t[i]]) : r = i = 1
          }
        }();
        var B = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
          r = r.AES = e.extend({
            _doReset: function () {
              if (!this._nRounds || this._keyPriorReset !== this._key) {
                for (var t = this._keyPriorReset = this._key, e = t.words, r = t.sigBytes / 4, i = 4 * (1 + (this._nRounds = 6 + r)), n = this._keySchedule = [], o = 0; o < i; o++) o < r ? n[o] = e[o] : (a = n[o - 1], o % r ? 6 < r && o % r == 4 && (a = h[a >>> 24] << 24 | h[a >>> 16 & 255] << 16 | h[a >>> 8 & 255] << 8 | h[255 & a]) : (a = h[(a = a << 8 | a >>> 24) >>> 24] << 24 | h[a >>> 16 & 255] << 16 | h[a >>> 8 & 255] << 8 | h[255 & a], a ^= B[o / r | 0] << 24), n[o] = n[o - r] ^ a);
                for (var s = this._invKeySchedule = [], c = 0; c < i; c++) {
                  var a, o = i - c;
                  a = c % 4 ? n[o] : n[o - 4], s[c] = c < 4 || o <= 4 ? a : _[h[a >>> 24]] ^ y[h[a >>> 16 & 255]] ^ v[h[a >>> 8 & 255]] ^ g[h[255 & a]]
                }
              }
            },
            encryptBlock: function (t, e) {
              this._doCryptBlock(t, e, this._keySchedule, f, d, u, p, h)
            },
            decryptBlock: function (t, e) {
              var r = t[e + 1];
              t[e + 1] = t[e + 3], t[e + 3] = r, this._doCryptBlock(t, e, this._invKeySchedule, _, y, v, g, l);
              r = t[e + 1];
              t[e + 1] = t[e + 3], t[e + 3] = r
            },
            _doCryptBlock: function (t, e, r, i, n, o, s, c) {
              for (var a = this._nRounds, h = t[e] ^ r[0], l = t[e + 1] ^ r[1], f = t[e + 2] ^ r[2], d = t[e + 3] ^ r[3], u = 4, p = 1; p < a; p++) var _ = i[h >>> 24] ^ n[l >>> 16 & 255] ^ o[f >>> 8 & 255] ^ s[255 & d] ^ r[u++],
                y = i[l >>> 24] ^ n[f >>> 16 & 255] ^ o[d >>> 8 & 255] ^ s[255 & h] ^ r[u++],
                v = i[f >>> 24] ^ n[d >>> 16 & 255] ^ o[h >>> 8 & 255] ^ s[255 & l] ^ r[u++],
                g = i[d >>> 24] ^ n[h >>> 16 & 255] ^ o[l >>> 8 & 255] ^ s[255 & f] ^ r[u++],
                h = _,
                l = y,
                f = v,
                d = g;
              _ = (c[h >>> 24] << 24 | c[l >>> 16 & 255] << 16 | c[f >>> 8 & 255] << 8 | c[255 & d]) ^ r[u++], y = (c[l >>> 24] << 24 | c[f >>> 16 & 255] << 16 | c[d >>> 8 & 255] << 8 | c[255 & h]) ^ r[u++], v = (c[f >>> 24] << 24 | c[d >>> 16 & 255] << 16 | c[h >>> 8 & 255] << 8 | c[255 & l]) ^ r[u++], g = (c[d >>> 24] << 24 | c[h >>> 16 & 255] << 16 | c[l >>> 8 & 255] << 8 | c[255 & f]) ^ r[u++];
              t[e] = _, t[e + 1] = y, t[e + 2] = v, t[e + 3] = g
            },
            keySize: 8
          });
        t.AES = e._createHelper(r)
      }(),
      function () {
        var t = U,
          e = t.lib,
          i = e.WordArray,
          r = e.BlockCipher,
          e = t.algo,
          h = [57, 49, 41, 33, 25, 17, 9, 1, 58, 50, 42, 34, 26, 18, 10, 2, 59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36, 63, 55, 47, 39, 31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37, 29, 21, 13, 5, 28, 20, 12, 4],
          l = [14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26, 8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32],
          f = [1, 2, 4, 6, 8, 10, 12, 14, 15, 17, 19, 21, 23, 25, 27, 28],
          d = [{
            0: 8421888,
            268435456: 32768,
            536870912: 8421378,
            805306368: 2,
            1073741824: 512,
            1342177280: 8421890,
            1610612736: 8389122,
            1879048192: 8388608,
            2147483648: 514,
            2415919104: 8389120,
            2684354560: 33280,
            2952790016: 8421376,
            3221225472: 32770,
            3489660928: 8388610,
            3758096384: 0,
            4026531840: 33282,
            134217728: 0,
            402653184: 8421890,
            671088640: 33282,
            939524096: 32768,
            1207959552: 8421888,
            1476395008: 512,
            1744830464: 8421378,
            2013265920: 2,
            2281701376: 8389120,
            2550136832: 33280,
            2818572288: 8421376,
            3087007744: 8389122,
            3355443200: 8388610,
            3623878656: 32770,
            3892314112: 514,
            4160749568: 8388608,
            1: 32768,
            268435457: 2,
            536870913: 8421888,
            805306369: 8388608,
            1073741825: 8421378,
            1342177281: 33280,
            1610612737: 512,
            1879048193: 8389122,
            2147483649: 8421890,
            2415919105: 8421376,
            2684354561: 8388610,
            2952790017: 33282,
            3221225473: 514,
            3489660929: 8389120,
            3758096385: 32770,
            4026531841: 0,
            134217729: 8421890,
            402653185: 8421376,
            671088641: 8388608,
            939524097: 512,
            1207959553: 32768,
            1476395009: 8388610,
            1744830465: 2,
            2013265921: 33282,
            2281701377: 32770,
            2550136833: 8389122,
            2818572289: 514,
            3087007745: 8421888,
            3355443201: 8389120,
            3623878657: 0,
            3892314113: 33280,
            4160749569: 8421378
          }, {
            0: 1074282512,
            16777216: 16384,
            33554432: 524288,
            50331648: 1074266128,
            67108864: 1073741840,
            83886080: 1074282496,
            100663296: 1073758208,
            117440512: 16,
            134217728: 540672,
            150994944: 1073758224,
            167772160: 1073741824,
            184549376: 540688,
            201326592: 524304,
            218103808: 0,
            234881024: 16400,
            251658240: 1074266112,
            8388608: 1073758208,
            25165824: 540688,
            41943040: 16,
            58720256: 1073758224,
            75497472: 1074282512,
            92274688: 1073741824,
            109051904: 524288,
            125829120: 1074266128,
            142606336: 524304,
            159383552: 0,
            176160768: 16384,
            192937984: 1074266112,
            209715200: 1073741840,
            226492416: 540672,
            243269632: 1074282496,
            260046848: 16400,
            268435456: 0,
            285212672: 1074266128,
            301989888: 1073758224,
            318767104: 1074282496,
            335544320: 1074266112,
            352321536: 16,
            369098752: 540688,
            385875968: 16384,
            402653184: 16400,
            419430400: 524288,
            436207616: 524304,
            452984832: 1073741840,
            469762048: 540672,
            486539264: 1073758208,
            503316480: 1073741824,
            520093696: 1074282512,
            276824064: 540688,
            293601280: 524288,
            310378496: 1074266112,
            327155712: 16384,
            343932928: 1073758208,
            360710144: 1074282512,
            377487360: 16,
            394264576: 1073741824,
            411041792: 1074282496,
            427819008: 1073741840,
            444596224: 1073758224,
            461373440: 524304,
            478150656: 0,
            494927872: 16400,
            511705088: 1074266128,
            528482304: 540672
          }, {
            0: 260,
            1048576: 0,
            2097152: 67109120,
            3145728: 65796,
            4194304: 65540,
            5242880: 67108868,
            6291456: 67174660,
            7340032: 67174400,
            8388608: 67108864,
            9437184: 67174656,
            10485760: 65792,
            11534336: 67174404,
            12582912: 67109124,
            13631488: 65536,
            14680064: 4,
            15728640: 256,
            524288: 67174656,
            1572864: 67174404,
            2621440: 0,
            3670016: 67109120,
            4718592: 67108868,
            5767168: 65536,
            6815744: 65540,
            7864320: 260,
            8912896: 4,
            9961472: 256,
            11010048: 67174400,
            12058624: 65796,
            13107200: 65792,
            14155776: 67109124,
            15204352: 67174660,
            16252928: 67108864,
            16777216: 67174656,
            17825792: 65540,
            18874368: 65536,
            19922944: 67109120,
            20971520: 256,
            22020096: 67174660,
            23068672: 67108868,
            24117248: 0,
            25165824: 67109124,
            26214400: 67108864,
            27262976: 4,
            28311552: 65792,
            29360128: 67174400,
            30408704: 260,
            31457280: 65796,
            32505856: 67174404,
            17301504: 67108864,
            18350080: 260,
            19398656: 67174656,
            20447232: 0,
            21495808: 65540,
            22544384: 67109120,
            23592960: 256,
            24641536: 67174404,
            25690112: 65536,
            26738688: 67174660,
            27787264: 65796,
            28835840: 67108868,
            29884416: 67109124,
            30932992: 67174400,
            31981568: 4,
            33030144: 65792
          }, {
            0: 2151682048,
            65536: 2147487808,
            131072: 4198464,
            196608: 2151677952,
            262144: 0,
            327680: 4198400,
            393216: 2147483712,
            458752: 4194368,
            524288: 2147483648,
            589824: 4194304,
            655360: 64,
            720896: 2147487744,
            786432: 2151678016,
            851968: 4160,
            917504: 4096,
            983040: 2151682112,
            32768: 2147487808,
            98304: 64,
            163840: 2151678016,
            229376: 2147487744,
            294912: 4198400,
            360448: 2151682112,
            425984: 0,
            491520: 2151677952,
            557056: 4096,
            622592: 2151682048,
            688128: 4194304,
            753664: 4160,
            819200: 2147483648,
            884736: 4194368,
            950272: 4198464,
            1015808: 2147483712,
            1048576: 4194368,
            1114112: 4198400,
            1179648: 2147483712,
            1245184: 0,
            1310720: 4160,
            1376256: 2151678016,
            1441792: 2151682048,
            1507328: 2147487808,
            1572864: 2151682112,
            1638400: 2147483648,
            1703936: 2151677952,
            1769472: 4198464,
            1835008: 2147487744,
            1900544: 4194304,
            1966080: 64,
            2031616: 4096,
            1081344: 2151677952,
            1146880: 2151682112,
            1212416: 0,
            1277952: 4198400,
            1343488: 4194368,
            1409024: 2147483648,
            1474560: 2147487808,
            1540096: 64,
            1605632: 2147483712,
            1671168: 4096,
            1736704: 2147487744,
            1802240: 2151678016,
            1867776: 4160,
            1933312: 2151682048,
            1998848: 4194304,
            2064384: 4198464
          }, {
            0: 128,
            4096: 17039360,
            8192: 262144,
            12288: 536870912,
            16384: 537133184,
            20480: 16777344,
            24576: 553648256,
            28672: 262272,
            32768: 16777216,
            36864: 537133056,
            40960: 536871040,
            45056: 553910400,
            49152: 553910272,
            53248: 0,
            57344: 17039488,
            61440: 553648128,
            2048: 17039488,
            6144: 553648256,
            10240: 128,
            14336: 17039360,
            18432: 262144,
            22528: 537133184,
            26624: 553910272,
            30720: 536870912,
            34816: 537133056,
            38912: 0,
            43008: 553910400,
            47104: 16777344,
            51200: 536871040,
            55296: 553648128,
            59392: 16777216,
            63488: 262272,
            65536: 262144,
            69632: 128,
            73728: 536870912,
            77824: 553648256,
            81920: 16777344,
            86016: 553910272,
            90112: 537133184,
            94208: 16777216,
            98304: 553910400,
            102400: 553648128,
            106496: 17039360,
            110592: 537133056,
            114688: 262272,
            118784: 536871040,
            122880: 0,
            126976: 17039488,
            67584: 553648256,
            71680: 16777216,
            75776: 17039360,
            79872: 537133184,
            83968: 536870912,
            88064: 17039488,
            92160: 128,
            96256: 553910272,
            100352: 262272,
            104448: 553910400,
            108544: 0,
            112640: 553648128,
            116736: 16777344,
            120832: 262144,
            124928: 537133056,
            129024: 536871040
          }, {
            0: 268435464,
            256: 8192,
            512: 270532608,
            768: 270540808,
            1024: 268443648,
            1280: 2097152,
            1536: 2097160,
            1792: 268435456,
            2048: 0,
            2304: 268443656,
            2560: 2105344,
            2816: 8,
            3072: 270532616,
            3328: 2105352,
            3584: 8200,
            3840: 270540800,
            128: 270532608,
            384: 270540808,
            640: 8,
            896: 2097152,
            1152: 2105352,
            1408: 268435464,
            1664: 268443648,
            1920: 8200,
            2176: 2097160,
            2432: 8192,
            2688: 268443656,
            2944: 270532616,
            3200: 0,
            3456: 270540800,
            3712: 2105344,
            3968: 268435456,
            4096: 268443648,
            4352: 270532616,
            4608: 270540808,
            4864: 8200,
            5120: 2097152,
            5376: 268435456,
            5632: 268435464,
            5888: 2105344,
            6144: 2105352,
            6400: 0,
            6656: 8,
            6912: 270532608,
            7168: 8192,
            7424: 268443656,
            7680: 270540800,
            7936: 2097160,
            4224: 8,
            4480: 2105344,
            4736: 2097152,
            4992: 268435464,
            5248: 268443648,
            5504: 8200,
            5760: 270540808,
            6016: 270532608,
            6272: 270540800,
            6528: 270532616,
            6784: 8192,
            7040: 2105352,
            7296: 2097160,
            7552: 0,
            7808: 268435456,
            8064: 268443656
          }, {
            0: 1048576,
            16: 33555457,
            32: 1024,
            48: 1049601,
            64: 34604033,
            80: 0,
            96: 1,
            112: 34603009,
            128: 33555456,
            144: 1048577,
            160: 33554433,
            176: 34604032,
            192: 34603008,
            208: 1025,
            224: 1049600,
            240: 33554432,
            8: 34603009,
            24: 0,
            40: 33555457,
            56: 34604032,
            72: 1048576,
            88: 33554433,
            104: 33554432,
            120: 1025,
            136: 1049601,
            152: 33555456,
            168: 34603008,
            184: 1048577,
            200: 1024,
            216: 34604033,
            232: 1,
            248: 1049600,
            256: 33554432,
            272: 1048576,
            288: 33555457,
            304: 34603009,
            320: 1048577,
            336: 33555456,
            352: 34604032,
            368: 1049601,
            384: 1025,
            400: 34604033,
            416: 1049600,
            432: 1,
            448: 0,
            464: 34603008,
            480: 33554433,
            496: 1024,
            264: 1049600,
            280: 33555457,
            296: 34603009,
            312: 1,
            328: 33554432,
            344: 1048576,
            360: 1025,
            376: 34604032,
            392: 33554433,
            408: 34603008,
            424: 0,
            440: 34604033,
            456: 1049601,
            472: 1024,
            488: 33555456,
            504: 1048577
          }, {
            0: 134219808,
            1: 131072,
            2: 134217728,
            3: 32,
            4: 131104,
            5: 134350880,
            6: 134350848,
            7: 2048,
            8: 134348800,
            9: 134219776,
            10: 133120,
            11: 134348832,
            12: 2080,
            13: 0,
            14: 134217760,
            15: 133152,
            2147483648: 2048,
            2147483649: 134350880,
            2147483650: 134219808,
            2147483651: 134217728,
            2147483652: 134348800,
            2147483653: 133120,
            2147483654: 133152,
            2147483655: 32,
            2147483656: 134217760,
            2147483657: 2080,
            2147483658: 131104,
            2147483659: 134350848,
            2147483660: 0,
            2147483661: 134348832,
            2147483662: 134219776,
            2147483663: 131072,
            16: 133152,
            17: 134350848,
            18: 32,
            19: 2048,
            20: 134219776,
            21: 134217760,
            22: 134348832,
            23: 131072,
            24: 0,
            25: 131104,
            26: 134348800,
            27: 134219808,
            28: 134350880,
            29: 133120,
            30: 2080,
            31: 134217728,
            2147483664: 131072,
            2147483665: 2048,
            2147483666: 134348832,
            2147483667: 133152,
            2147483668: 32,
            2147483669: 134348800,
            2147483670: 134217728,
            2147483671: 134219808,
            2147483672: 134350880,
            2147483673: 134217760,
            2147483674: 134219776,
            2147483675: 0,
            2147483676: 133120,
            2147483677: 2080,
            2147483678: 131104,
            2147483679: 134350848
          }],
          u = [4160749569, 528482304, 33030144, 2064384, 129024, 8064, 504, 2147483679],
          n = e.DES = r.extend({
            _doReset: function () {
              for (var t = this._key.words, e = [], r = 0; r < 56; r++) {
                var i = h[r] - 1;
                e[r] = t[i >>> 5] >>> 31 - i % 32 & 1
              }
              for (var n = this._subKeys = [], o = 0; o < 16; o++) {
                for (var s = n[o] = [], c = f[o], r = 0; r < 24; r++) s[r / 6 | 0] |= e[(l[r] - 1 + c) % 28] << 31 - r % 6, s[4 + (r / 6 | 0)] |= e[28 + (l[r + 24] - 1 + c) % 28] << 31 - r % 6;
                s[0] = s[0] << 1 | s[0] >>> 31;
                for (r = 1; r < 7; r++) s[r] = s[r] >>> 4 * (r - 1) + 3;
                s[7] = s[7] << 5 | s[7] >>> 27
              }
              for (var a = this._invSubKeys = [], r = 0; r < 16; r++) a[r] = n[15 - r]
            },
            encryptBlock: function (t, e) {
              this._doCryptBlock(t, e, this._subKeys)
            },
            decryptBlock: function (t, e) {
              this._doCryptBlock(t, e, this._invSubKeys)
            },
            _doCryptBlock: function (t, e, r) {
              this._lBlock = t[e], this._rBlock = t[e + 1], p.call(this, 4, 252645135), p.call(this, 16, 65535), _.call(this, 2, 858993459), _.call(this, 8, 16711935), p.call(this, 1, 1431655765);
              for (var i = 0; i < 16; i++) {
                for (var n = r[i], o = this._lBlock, s = this._rBlock, c = 0, a = 0; a < 8; a++) c |= d[a][((s ^ n[a]) & u[a]) >>> 0];
                this._lBlock = s, this._rBlock = o ^ c
              }
              var h = this._lBlock;
              this._lBlock = this._rBlock, this._rBlock = h, p.call(this, 1, 1431655765), _.call(this, 8, 16711935), _.call(this, 2, 858993459), p.call(this, 16, 65535), p.call(this, 4, 252645135), t[e] = this._lBlock, t[e + 1] = this._rBlock
            },
            keySize: 2,
            ivSize: 2,
            blockSize: 2
          });

        function p(t, e) {
          e = (this._lBlock >>> t ^ this._rBlock) & e;
          this._rBlock ^= e, this._lBlock ^= e << t
        }

        function _(t, e) {
          e = (this._rBlock >>> t ^ this._lBlock) & e;
          this._lBlock ^= e, this._rBlock ^= e << t
        }
        t.DES = r._createHelper(n);
        e = e.TripleDES = r.extend({
          _doReset: function () {
            var t = this._key.words;
            if (2 !== t.length && 4 !== t.length && t.length < 6) throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");
            var e = t.slice(0, 2),
              r = t.length < 4 ? t.slice(0, 2) : t.slice(2, 4),
              t = t.length < 6 ? t.slice(0, 2) : t.slice(4, 6);
            this._des1 = n.createEncryptor(i.create(e)), this._des2 = n.createEncryptor(i.create(r)), this._des3 = n.createEncryptor(i.create(t))
          },
          encryptBlock: function (t, e) {
            this._des1.encryptBlock(t, e), this._des2.decryptBlock(t, e), this._des3.encryptBlock(t, e)
          },
          decryptBlock: function (t, e) {
            this._des3.decryptBlock(t, e), this._des2.encryptBlock(t, e), this._des1.decryptBlock(t, e)
          },
          keySize: 6,
          ivSize: 2,
          blockSize: 2
        });
        t.TripleDES = r._createHelper(e)
      }(),
      function () {
        var t = U,
          e = t.lib.StreamCipher,
          r = t.algo,
          i = r.RC4 = e.extend({
            _doReset: function () {
              for (var t = this._key, e = t.words, r = t.sigBytes, i = this._S = [], n = 0; n < 256; n++) i[n] = n;
              for (var n = 0, o = 0; n < 256; n++) {
                var s = n % r,
                  s = e[s >>> 2] >>> 24 - s % 4 * 8 & 255,
                  o = (o + i[n] + s) % 256,
                  s = i[n];
                i[n] = i[o], i[o] = s
              }
              this._i = this._j = 0
            },
            _doProcessBlock: function (t, e) {
              t[e] ^= n.call(this)
            },
            keySize: 8,
            ivSize: 0
          });

        function n() {
          for (var t = this._S, e = this._i, r = this._j, i = 0, n = 0; n < 4; n++) {
            var r = (r + t[e = (e + 1) % 256]) % 256,
              o = t[e];
            t[e] = t[r], t[r] = o, i |= t[(t[e] + t[r]) % 256] << 24 - 8 * n
          }
          return this._i = e, this._j = r, i
        }
        t.RC4 = e._createHelper(i);
        r = r.RC4Drop = i.extend({
          cfg: i.cfg.extend({
            drop: 192
          }),
          _doReset: function () {
            i._doReset.call(this);
            for (var t = this.cfg.drop; 0 < t; t--) n.call(this)
          }
        });
        t.RC4Drop = e._createHelper(r)
      }(), F = (M = U).lib.StreamCipher, P = M.algo, D = [], E = [], R = [], P = P.Rabbit = F.extend({
        _doReset: function () {
          for (var t = this._key.words, e = this.cfg.iv, r = 0; r < 4; r++) t[r] = 16711935 & (t[r] << 8 | t[r] >>> 24) | 4278255360 & (t[r] << 24 | t[r] >>> 8);
          for (var i = this._X = [t[0], t[3] << 16 | t[2] >>> 16, t[1], t[0] << 16 | t[3] >>> 16, t[2], t[1] << 16 | t[0] >>> 16, t[3], t[2] << 16 | t[1] >>> 16], n = this._C = [t[2] << 16 | t[2] >>> 16, 4294901760 & t[0] | 65535 & t[1], t[3] << 16 | t[3] >>> 16, 4294901760 & t[1] | 65535 & t[2], t[0] << 16 | t[0] >>> 16, 4294901760 & t[2] | 65535 & t[3], t[1] << 16 | t[1] >>> 16, 4294901760 & t[3] | 65535 & t[0]], r = this._b = 0; r < 4; r++) N.call(this);
          for (r = 0; r < 8; r++) n[r] ^= i[r + 4 & 7];
          if (e) {
            var o = e.words,
              s = o[0],
              c = o[1],
              e = 16711935 & (s << 8 | s >>> 24) | 4278255360 & (s << 24 | s >>> 8),
              o = 16711935 & (c << 8 | c >>> 24) | 4278255360 & (c << 24 | c >>> 8),
              s = e >>> 16 | 4294901760 & o,
              c = o << 16 | 65535 & e;
            n[0] ^= e, n[1] ^= s, n[2] ^= o, n[3] ^= c, n[4] ^= e, n[5] ^= s, n[6] ^= o, n[7] ^= c;
            for (r = 0; r < 4; r++) N.call(this)
          }
        },
        _doProcessBlock: function (t, e) {
          var r = this._X;
          N.call(this), D[0] = r[0] ^ r[5] >>> 16 ^ r[3] << 16, D[1] = r[2] ^ r[7] >>> 16 ^ r[5] << 16, D[2] = r[4] ^ r[1] >>> 16 ^ r[7] << 16, D[3] = r[6] ^ r[3] >>> 16 ^ r[1] << 16;
          for (var i = 0; i < 4; i++) D[i] = 16711935 & (D[i] << 8 | D[i] >>> 24) | 4278255360 & (D[i] << 24 | D[i] >>> 8), t[e + i] ^= D[i]
        },
        blockSize: 4,
        ivSize: 2
      }), M.Rabbit = F._createHelper(P), F = (M = U).lib.StreamCipher, P = M.algo, W = [], O = [], I = [], P = P.RabbitLegacy = F.extend({
        _doReset: function () {
          for (var t = this._key.words, e = this.cfg.iv, r = this._X = [t[0], t[3] << 16 | t[2] >>> 16, t[1], t[0] << 16 | t[3] >>> 16, t[2], t[1] << 16 | t[0] >>> 16, t[3], t[2] << 16 | t[1] >>> 16], i = this._C = [t[2] << 16 | t[2] >>> 16, 4294901760 & t[0] | 65535 & t[1], t[3] << 16 | t[3] >>> 16, 4294901760 & t[1] | 65535 & t[2], t[0] << 16 | t[0] >>> 16, 4294901760 & t[2] | 65535 & t[3], t[1] << 16 | t[1] >>> 16, 4294901760 & t[3] | 65535 & t[0]], n = this._b = 0; n < 4; n++) q.call(this);
          for (n = 0; n < 8; n++) i[n] ^= r[n + 4 & 7];
          if (e) {
            var o = e.words,
              s = o[0],
              t = o[1],
              e = 16711935 & (s << 8 | s >>> 24) | 4278255360 & (s << 24 | s >>> 8),
              o = 16711935 & (t << 8 | t >>> 24) | 4278255360 & (t << 24 | t >>> 8),
              s = e >>> 16 | 4294901760 & o,
              t = o << 16 | 65535 & e;
            i[0] ^= e, i[1] ^= s, i[2] ^= o, i[3] ^= t, i[4] ^= e, i[5] ^= s, i[6] ^= o, i[7] ^= t;
            for (n = 0; n < 4; n++) q.call(this)
          }
        },
        _doProcessBlock: function (t, e) {
          var r = this._X;
          q.call(this), W[0] = r[0] ^ r[5] >>> 16 ^ r[3] << 16, W[1] = r[2] ^ r[7] >>> 16 ^ r[5] << 16, W[2] = r[4] ^ r[1] >>> 16 ^ r[7] << 16, W[3] = r[6] ^ r[3] >>> 16 ^ r[1] << 16;
          for (var i = 0; i < 4; i++) W[i] = 16711935 & (W[i] << 8 | W[i] >>> 24) | 4278255360 & (W[i] << 24 | W[i] >>> 8), t[e + i] ^= W[i]
        },
        blockSize: 4,
        ivSize: 2
      }), M.RabbitLegacy = F._createHelper(P), U
  });
} catch (err) {

}
// ========================================================== decrypt方法end =================================================

// ========================================================== 导出函数start =================================================

Global.P8QuickGameSDK = P8QuickGameSDK;

// ========================================================== 导出函数end =================================================