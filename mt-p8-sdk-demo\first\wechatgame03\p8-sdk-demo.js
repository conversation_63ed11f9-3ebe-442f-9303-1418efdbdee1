try {
  if (typeof mt !== 'undefined') {
    require('./p8sdk-quickGame-1.0.4.js')
  } else if (typeof wx !== 'undefined') {
    require('./p8sdk-quickGame-1.0.4.js')
  } else {
    console.error('请美团小游戏或者微信小游戏环境中运行');
  }
} catch (err) {
  console.error('请美团小游戏或者微信小游戏环境中运行');
}
console.log('[ 进入demo了 ] >', 'Hello World')

let btnConfigs = [{
    label: '初始化', // 按钮名
    cbk: () => {
      console.log('初始化点击');
      console.log('[ window.P8QuickGameSDK ] >', window.P8QuickGameSDK)
      let initData = {
        appName: '超级逗萌兽', //游戏名
        site: 'cjdms_android7', // 问运营
        key: 'ca8e1f9a56d243cd1580423679bf060c', // 问运营
        aid: '936299171116027690', // 问运营
        appid: 'mgc8hf4rckwxpfjv', // 小程序appid
        pkgName: '1', // 包名
        platform: 'meituan', // 平台 oppo vivo honor huaweiRuntime meituan
        channelid: "46", // oppo：241 vivo:256 荣耀：43 华为：267 美团：46
      }
      P8QuickGameSDK.init(initData).then((res) => {
        console.log('[聚合SDK] 初始化返回数据res:', JSON.stringify(res));
      });
    },
  },
  {
    label: '登录', // 按钮名
    cbk: () => {
      console.log('登录点击');
      P8QuickGameSDK.login().then((res) => {
        console.info('【聚合快游戏登录】返回数据:', JSON.stringify(res));
      })
    }
  },
  {
    label: '支付', // 按钮名
    cbk: () => {
      console.log('支付点击');
      let payData = {
        roleId: '456123', //角色ID
        userName: 'Adien', //用户名
        grade: '98', //玩家等级
        serverId: '1001', // CP服务器id
        productDesc: '测试支付接口', //商品描述(不能为空)，该字段中不能包含特殊字符，包括# " & / ? $ ^ *:) \ < > , | % + 
        productName: '测试支付接口', //商品名称，该字段中不能包含特殊字符，包括# " & / ? $ ^ *:) \ < > , | % + 
        count: '1', // 数量
        price: '100', //商品金额 单位分
        cpOrderId: 'meituan_' + new Date().getTime(), //CP自己的订单号
        productId: "1", // 商品id 不清楚问运营
        appVersion: '1.0.0', // 游戏版本
        // 非必填
        extraInfo: {
          test: 'test'
        }, // 透传的拓展参数 如需使用请传入对象object
        needSandboxTest: 0, // 1 表示沙盒支付，0 表示正式支付，默认值为0 仅荣耀小游戏使用
        productUrl: "", // 内购商品图片链接 仅美团小游戏使用
        needRefresh: 1, // 0： 支付完成后游戏会重新加载。1：支付完成后会回到之前的游戏，游戏不会重新加载 仅美团小游戏使用
      }
      console.log('[ payData ] >', JSON.stringify(payData))
      P8QuickGameSDK.pay(payData).then((res) => {
        console.log('【聚合SDK】 支付返回数据res:', JSON.stringify(res));
      })

      // P8QuickGameSDK.pay(payData).then((res) => {
      //   console.info('【聚合快游戏支付】返回数据:', JSON.stringify(res));
      // })
    }
  },
  {
    label: '激活上报', // 按钮名
    cbk: () => {
      console.log('[ 激活上报点击 ] >')
      P8QuickGameSDK.onActiveFunc().then((res) => {
        console.log('[聚合SDK] 激活上报返回数据res:', JSON.stringify(res));
      });

      // P8QuickGameSDK.onActiveFunc().then((res) => {
      //   console.info('【聚合快游戏激活上报】返回数据:', JSON.stringify(res));
      // })
    }
  },
  {
    label: '登录上报', // 按钮名
    cbk: () => {
      console.log('[ 登录上报点击 ] >')
      let loginData = {
        // 必填
        sid: '1001', // 游戏服务器ID
        roleid: '456123', // 游戏角色ID
        rolename: 'Adien', // 游戏角色名称
        level: '98', // 登录时角色等级
        vip: '1', // vip等级
        // 选填
        oaid: '', // oaid, 安卓必须传,获取不了传空, ios不传
        username: '', // Play800 sdk 用户登录账号
        onlinetime: '', // 角色累计在线时长（单位分钟）
      }
      P8QuickGameSDK.pushLoginData(loginData).then((data) => {
        console.log('[聚合SDK] 登录上报返回数据res:', JSON.stringify(data));
        // 初始化成功 
      });

      // P8QuickGameSDK.pushLoginData(loginData).then((data) => {
      //   console.info('【聚合快游戏登录上报】返回数据:', JSON.stringify(data));
      // })
    }
  },
  {
    label: '新创角色上报',
    cbk: () => {
      let login_order = {
        // 必填
        sid: '1001', // 服务器 ID
        roleid: '456123', // 角色 ID
        rolename: 'Adien', // 角色名
        level: '1', // 角色等级
      }
      P8QuickGameSDK.signLog(login_order).then((res) => {
        console.log('[聚合SDK] 新创角色上报返回数据res:', JSON.stringify(res));
      })

      // P8QuickGameSDK.signLog(login_order).then((res) => {
      //   console.info('【聚合快游戏新创角色上报】返回数据:', JSON.stringify(res));
      // })
    }
  },
  {
    label: '角色升级上报',
    cbk: () => {
      let gradeData = {
        level: '99', //用户等级
        sid: '1001', // 服务器id
        roleid: '456123', // 角色id
        rolename: 'Adien', //角色名
        vip: '1',
        // 非必传
        onlinetime: '',
        oaid: '',
      }
      P8QuickGameSDK.upGradeRecord(gradeData).then((data) => {
        console.log('[聚合SDK] 角色升级上报返回数据res:', JSON.stringify(data));
      })

      // P8QuickGameSDK.upGradeRecord(gradeData).then((data) => {
      //   console.info('【聚合快游戏角色升级上报】返回数据:', JSON.stringify(data));
      // })
    }
  },
  {
    label: '广告初始化', // 按钮名
    cbk: () => {
      console.log('[ 广告初始化点击 ] >')
      P8QuickGameSDK.videoADInit({
        adUnitId: '10302',
        adSlot: '超级逗萌兽美团小游戏激励视频',
        success: (res) => {
          console.log('[聚合SDK] 激励视频 广告加载事件成功 initalization', JSON.stringify(res))
        },
        fail: (err) => {
          console.log('[聚合SDK] 激励视频 广告加载异常 initalization', JSON.stringify(err))
        }
      });
    }
  },
  {
    label: '激励视频广告', // 按钮名
    cbk: () => {
      console.log('[ 激励视频广告点击 ] >')
      P8QuickGameSDK.videoADShow(() => {
        console.log('videoADShow Success');
        mt.showToast({
          title: '广告播放成功',
          icon: 'success',
          duration: 3000
        })
      }, () => {
        console.log('videoADShow Close');
        mt.showToast({
          title: '广告播放关闭',
          icon: 'none',
          duration: 3000
        })
      }, (err) => {
        console.log('videoADShow Fail' + JSON.stringify(err));
        mt.showToast({
          title: '广告播放失败',
          icon: 'error',
          duration: 3000
        })
      }, () => {
        console.log('videoADShow Show');
        mt.showToast({
          title: '广告播放中',
          icon: 'none',
          duration: 3000
        })
      }, '6666')
    }
  },
  {
    label: '广告上报',
    cbk: () => {
      let arg = {
        sid: '1001', // 游戏服id
        roleid: '456123', //角色id
        rolename: 'Adien', // 角色名
        level: '98', // 角色等级
        ad_slot: '超级逗萌兽美团小游戏激励视频', // 广告位创建的名称 在微信后台申请的广告位的名称
        ad_unit_id: '10302', //广告位id
        type: 'RewardedVideoAd', // 'BannerAd' 横幅 'RewardedVideoAd' 激励视频 'InterstitialAd' 插屏广告 'CustomAd' 模板广告
        status: '0', // 点击传入 0 观看成功传入 1 banner广告点击就算成功
      }
      P8QuickGameSDK.wxVideoAutoLog(arg).then((res) => {
        console.log('[聚合SDK] 广告上报返回数据res:', JSON.stringify(res));
      })
    }
  },
]

let base = cc.find('CanvasTest');
let offsetPosY = 0;
let offsetPosY2 = 0;
let windowHeight = base.height;
let windowWidth = base.width;
btnConfigs.forEach(element => {
  creatorBtn(element.label, element.cbk);
});

// btnConfigs2.forEach(element => {
//   creatorBtn2(element.label, element.cbk);
// });

// let ctx = canvas.getContext('2d');
// ctx.fillStyle = '#ffffff';
// ctx.fillRect(20, 150, canvas.width - 40, canvas.height - 250);

// setTimeout(() => {
//   ctx.clearRect(20, 150, canvas.width - 40, canvas.height - 250)
// }, 1500)


// 设置输入框节点的位置和尺寸

function createWhite() {
  const texture = new cc.Texture2D();
  const image = new Uint8Array([255, 255, 255, 255]);
  texture.initWithData(image, cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
  const spriteFrame = new cc.SpriteFrame();
  spriteFrame.setTexture(texture);
  return spriteFrame;
}

function createNode(name, parent = null, set = null) {
  let node = new cc.Node(name);
  if (parent) {
    node.parent = parent;
  } else {
    node.parent = cc.find('CanvasTest')
  }
  if (set && Object(set) === set) {
    Object.assign(node, set)
  }
  node.zIndex = cc.macro.MAX_ZINDEX; //- indexDis;
  return node;
}

function addComponent(node, type, set = null) {
  let cmp = node.addComponent(type);
  if (set && Object(set) === set) {
    Object.assign(cmp, set)
  }
  return cmp;
}
GameGlobal.createNode = createNode;
GameGlobal.addComponent = addComponent;

function hexToColor(hex) {
  const color = new cc.Color();
  color.r = parseInt(hex.substr(1, 2), 16);
  color.g = parseInt(hex.substr(3, 2), 16);
  color.b = parseInt(hex.substr(5, 2), 16);
  return color;
}

function creatorBtn(label, cbk) {
  let posx = 300,
    height = 80,
    dis = 30,
    offset = 90;
  if (!offsetPosY) {
    offsetPosY = windowHeight / 2 - height / 2 - dis
  } else {
    offsetPosY -= offset;
  }
  let svnode = createNode('scNode', null, {
    width: posx,
    height: height,
    x: -windowWidth / 2 + posx / 2 + dis,
    y: offsetPosY
  });
  let localSF = createWhite();
  // console.log('localSF:', localSF);
  addComponent(svnode, cc.Sprite, {
    sizeMode: cc.Sprite.SizeMode.CUSTOM,
    spriteFrame: localSF
  });
  svnode.on(cc.Node.EventType.TOUCH_END, cbk, this);
  let title = createNode(label, svnode, {
    x: 0,
    y: 0,
    color: cc.Color.BLACK
  });
  addComponent(title, cc.Label, {
    string: label,
    fontSize: 32,
    lineHeight: 30,
  });
}

// 后期放到sdk文件中
var CryptoJS = wx.CryptoJS; // require("./crypto-js/crypto-js");

// 辅助函数
function md5(data) {
  return CryptoJS.MD5(data).toString();
}

// 传入key之前要调用，不然结果不对
function parseKey(key) {
  return CryptoJS.enc.Utf8.parse(key);
}

// 解密过程
function decrypt(mode, cipherText, key, iv = null) {
  const uKey = parseKey(key);
  const uIv = parseKey(iv);
  let bytes = CryptoJS.AES.decrypt(cipherText, uKey, {
    iv: uIv,
    mode: mode,
    padding: CryptoJS.pad.Pkcs7
  });
  // console.log('bytes', bytes.toString(CryptoJS.enc.Base64));
  return bytes.toString(CryptoJS.enc.Utf8);
}

function test() {
  let key = "916002158107813254";
  let md5Key = md5(key);
  let data = 'Bv4uLpMxsOdEtTiQo6YTFBpVuu/b+E4lxSSESIEQjFIYKIxlqUNaeiRSj26qVlT1S10swrwIVUvu06NLdcHKS9UP2uuntv0E0loIE9/RfQAJ+8Qq6r6jjjANGP2rINX8dgvZ/nM88iiJbFEUn5o4OwPaBBzviHNZ3SRPKyoVNXxLXSzCvAhVS+7To0t1wcpL1Q/a66e2/QTSWggT39F9AKIfGU9WWBeMz740cDRBjOruuo+UljNY8yZWMUfwBF9UXt9s7V007/ba2JqINsOHxEtdLMK8CFVL7tOjS3XBykt+UfRgCHmFy61PvNIgfHetKm3v32an0gzsK1icwZEsG6GJ5hAmudMHMpvMaMGVLsAD+z1IqBi27L5Ze2IDV0TFS10swrwIVUvu06NLdcHKSzsbl41vqSWFIX08PoygdyCrHaXnFoX48CntpZxAzk3O7HzbcIfsP2dMh7ShSkYYKicbEl+QYrpMTas0CuCZ1WpLXSzCvAhVS+7To0t1wcpLAF47NQssT7Iq9RGAWnGLIU2EFoUzRGAiNXF5w1M8k+Be9rGne5mq5Nt69N2bYrPmOj6wcSi++n8epufewWigMUtdLMK8CFVL7tOjS3XByktc2XAGTBUCrZgEyQfPsMc7VsgwtLWQqU/A+UjJlM5q4NjuiILD57SI1cxoVh5HbuOL3Q4T2j6krPGRKK7vAgB3S10swrwIVUvu06NLdcHKS0ZGCSZPqsCezaw2eCQF3RuvGGxD0N3huF3fsMK2Yco69G2waBnFIDGFntx0wnFPFYvdDhPaPqSs8ZEoru8CAHdLXSzCvAhVS+7To0t1wcpLkoofGcxZmLk8Z+hlXLeQuDsZ4QPcqwfL/FAd06TLiUO/0uxkyOqqUiHu7Zmx4vQ7ciP0YtV3yA/kkqZY0PVc+UtdLMK8CFVL7tOjS3XByktTbVjwkNhW62fmPzHgLJxJWepBcrVvKkRR/m44pxFqB+hkr8E2HgvEbLu/M5xw71YD2gQc74hzWd0kTysqFTV8S10swrwIVUvu06NLdcHKS9UP2uuntv0E0loIE9/RfQDMIYmz27rt9VlhZHa9NYfFU2O8ReVTvDUCYEqlBGg0yLmpLnuzzO9WaJi5i4gFT5woxqTi/Yd9Tom/X3pbyJO7S10swrwIVUvu06NLdcHKS8jYONfmzm0X8IMj4iXPM+b4XxPT601esGoC6nG//Oe+'
  let plainText = decrypt(CryptoJS.mode.ECB, data, md5Key);
}

// test();

function creatorBtn2(label, cbk) {
  let posx = 300,
    height = 65,
    dis = 20,
    offset = 70;
  if (!offsetPosY2) {
    offsetPosY2 = windowHeight / 2 - height / 2 - dis;
  } else {
    offsetPosY2 = offsetPosY2 - offset;
  }
  let svnode = createNode('scNode', null, {
    width: posx,
    height: height,
    x: windowWidth / 2 - posx / 2 - dis, // 调整 x 的计算方式
    y: offsetPosY2
  });
  let localSF = createWhite();
  addComponent(svnode, cc.Sprite, {
    sizeMode: cc.Sprite.SizeMode.CUSTOM,
    spriteFrame: localSF
  });
  svnode.on(cc.Node.EventType.TOUCH_END, cbk, this);
  let title = createNode(label, svnode, {
    x: 0,
    y: 0,
    color: cc.Color.BLACK
  });
  addComponent(title, cc.Label, {
    string: label,
    fontSize: 32,
    lineHeight: 30,
  });
}